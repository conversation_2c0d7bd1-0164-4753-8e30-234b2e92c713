"""
Question generation service using OpenAI for AI-Examiner.
"""
import json
import uuid
from typing import List, Dict, Any
from openai import OpenAI
from app.config import settings
from app.database import db_manager
from app.models.question import QuestionCreate, Question, QuestionGenerationRequest
import logging

logger = logging.getLogger(__name__)

# Initialize OpenAI client
openai_client = OpenAI(api_key=settings.openai_api_key)


class QuestionGenerator:
    """Question generation service using OpenAI."""
    
    def __init__(self):
        self.db = db_manager
        self.client = openai_client
    
    def _get_mcq_prompt(self, text: str, count: int, difficulty: str) -> str:
        """Generate prompt for MCQ questions."""
        return f"""
Extract {count} multiple-choice questions from the following text with {difficulty} difficulty level.

For each question, provide:
1. A clear, specific question
2. Four options labeled A, B, C, D
3. The correct option letter
4. A brief explanation (1-2 sentences)

Text:
\"\"\"
{text}
\"\"\"

Format the output as a JSON array with this structure:
[
  {{
    "question": "Question text here",
    "options": [
      {{"label": "A", "text": "Option A text"}},
      {{"label": "B", "text": "Option B text"}},
      {{"label": "C", "text": "Option C text"}},
      {{"label": "D", "text": "Option D text"}}
    ],
    "correct_option": "A",
    "explanation": "Explanation here"
  }}
]

Ensure questions are relevant to the provided text and match the specified difficulty level.
"""
    
    def _get_coding_prompt(self, text: str, count: int, difficulty: str) -> str:
        """Generate prompt for coding questions."""
        return f"""
Based on the following text, generate {count} coding problems with {difficulty} difficulty level.

For each problem, provide:
1. A clear problem title
2. Detailed problem statement
3. Input format specification
4. Output format specification
5. Constraints
6. Sample input and output
7. At least 2 test cases

Text:
\"\"\"
{text}
\"\"\"

Format the output as a JSON array with this structure:
[
  {{
    "title": "Problem title",
    "problem_statement": "Detailed problem description",
    "input_format": "Input format description",
    "output_format": "Output format description",
    "constraints": ["Constraint 1", "Constraint 2"],
    "sample_input": "Sample input here",
    "sample_output": "Sample output here",
    "test_cases": [
      {{"input": "test input 1", "output": "expected output 1"}},
      {{"input": "test input 2", "output": "expected output 2"}}
    ]
  }}
]

Make problems suitable for programming interviews and match the specified difficulty.
"""
    
    def _get_aptitude_prompt(self, text: str, count: int, difficulty: str) -> str:
        """Generate prompt for aptitude questions."""
        return f"""
Generate {count} aptitude questions based on the following text with {difficulty} difficulty level.

Include questions on:
- Logical reasoning
- Quantitative aptitude
- Verbal reasoning
- Analytical thinking

For each question, provide:
1. Question text
2. Options (if multiple choice) or direct answer
3. Correct answer
4. Brief explanation
5. Topic category

Text:
\"\"\"
{text}
\"\"\"

Format the output as a JSON array with this structure:
[
  {{
    "question": "Question text here",
    "options": [
      {{"label": "A", "text": "Option A"}},
      {{"label": "B", "text": "Option B"}},
      {{"label": "C", "text": "Option C"}},
      {{"label": "D", "text": "Option D"}}
    ],
    "correct_answer": "A",
    "explanation": "Explanation here",
    "topic": "Logical Reasoning"
  }}
]

For non-MCQ questions, omit the options field and provide the direct answer in correct_answer.
"""
    
    async def _call_openai(self, prompt: str) -> List[Dict[str, Any]]:
        """Call OpenAI API with the given prompt."""
        try:
            response = self.client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert question generator for educational assessments. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            content = response.choices[0].message.content.strip()
            
            # Try to parse JSON response
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # If direct parsing fails, try to extract JSON from the response
                start_idx = content.find('[')
                end_idx = content.rfind(']') + 1
                if start_idx != -1 and end_idx != 0:
                    json_content = content[start_idx:end_idx]
                    return json.loads(json_content)
                else:
                    raise ValueError("No valid JSON found in response")
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise Exception(f"Failed to generate questions: {e}")
    
    async def generate_questions(self, request: QuestionGenerationRequest, user_id: str) -> str:
        """Generate questions from uploaded file."""
        try:
            # Get file upload record
            file_record = await self.db.get_file_upload(request.file_id)
            if not file_record:
                raise Exception("File not found")
            
            if file_record['processing_status'] != 'completed':
                raise Exception("File processing not completed")
            
            extracted_text = file_record['extracted_text']
            if not extracted_text or len(extracted_text.strip()) < 100:
                raise Exception("Insufficient text content for question generation")
            
            # Create question generation job
            job_data = {
                "id": str(uuid.uuid4()),
                "file_upload_id": request.file_id,
                "user_id": user_id,
                "job_status": "processing",
                "question_types": request.question_types
            }
            
            job = await self.db.create_question_job(job_data)
            job_id = job['id']
            
            # Generate questions for each type
            generated_questions = []
            total_generated = 0
            
            for question_type in request.question_types:
                try:
                    # Generate questions for each difficulty level
                    for difficulty, count in request.difficulty_distribution.items():
                        if count > 0:
                            questions = await self._generate_questions_by_type(
                                question_type, extracted_text, count, difficulty,
                                file_record['file_name'], user_id
                            )
                            generated_questions.extend(questions)
                            total_generated += len(questions)
                
                except Exception as e:
                    logger.error(f"Error generating {question_type} questions: {e}")
                    continue
            
            # Update job status
            if generated_questions:
                await self.db.update_question_job(job_id, {
                    "job_status": "completed",
                    "generated_count": total_generated
                })
                
                # Log question generation activity
                await self.db.log_activity({
                    "user_id": user_id,
                    "event_type": "question_generation",
                    "event_data": {
                        "job_id": job_id,
                        "file_id": request.file_id,
                        "generated_count": total_generated,
                        "question_types": request.question_types
                    }
                })
            else:
                await self.db.update_question_job(job_id, {
                    "job_status": "failed",
                    "error_message": "No questions could be generated"
                })
            
            return job_id
            
        except Exception as e:
            logger.error(f"Question generation error: {e}")
            if 'job_id' in locals():
                await self.db.update_question_job(job_id, {
                    "job_status": "failed",
                    "error_message": str(e)
                })
            raise Exception(f"Question generation failed: {e}")
    
    async def _generate_questions_by_type(
        self, question_type: str, text: str, count: int, difficulty: str,
        file_name: str, user_id: str
    ) -> List[Question]:
        """Generate questions of a specific type."""
        try:
            # Get appropriate prompt
            if question_type == "mcq":
                prompt = self._get_mcq_prompt(text, count, difficulty)
            elif question_type == "code":
                prompt = self._get_coding_prompt(text, count, difficulty)
            elif question_type == "aptitude":
                prompt = self._get_aptitude_prompt(text, count, difficulty)
            else:
                raise ValueError(f"Unsupported question type: {question_type}")
            
            # Call OpenAI
            ai_response = await self._call_openai(prompt)
            
            # Create question records
            questions = []
            for i, question_data in enumerate(ai_response):
                try:
                    # Create question title
                    if question_type == "code" and "title" in question_data:
                        title = question_data["title"]
                    else:
                        title = f"{question_type.upper()} Question {i+1}"
                    
                    question_create = QuestionCreate(
                        source_file=file_name,
                        file_name=file_name,
                        type=question_type,
                        title=title,
                        content=question_data,
                        difficulty=difficulty,
                        created_by=user_id
                    )
                    
                    # Save to database
                    question_record = await self.db.create_question(question_create.dict())
                    questions.append(Question(**question_record))
                    
                except Exception as e:
                    logger.error(f"Error creating question record: {e}")
                    continue
            
            return questions
            
        except Exception as e:
            logger.error(f"Error generating {question_type} questions: {e}")
            return []
    
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get question generation job status."""
        try:
            job = await self.db.get_question_job(job_id)
            if not job:
                raise Exception("Job not found")
            
            result = {
                "job_id": job_id,
                "status": job['job_status'],
                "generated_count": job['generated_count'],
                "question_types": job['question_types']
            }
            
            if job['error_message']:
                result["error_message"] = job['error_message']
            
            # If job is completed, get the generated questions
            if job['job_status'] == 'completed':
                questions = await self.db.get_questions({
                    "created_by": job['user_id']
                }, limit=100)
                
                # Filter questions created after this job
                job_questions = [
                    q for q in questions 
                    if q['created_at'] >= job['created_at']
                ]
                
                result["questions"] = job_questions
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting job status: {e}")
            raise Exception(f"Failed to get job status: {e}")


# Global question generator instance
question_generator = QuestionGenerator()
