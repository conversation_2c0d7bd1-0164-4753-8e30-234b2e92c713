"""
Database connection and session management for AI-Examiner.
"""
from supabase import create_client, Client
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.config import settings
import logging

logger = logging.getLogger(__name__)

# Supabase client
supabase: Client = create_client(
    settings.supabase_url,
    settings.supabase_service_key
)

# SQLAlchemy setup (for direct database operations if needed)
# Note: We primarily use Supabase client, but SQLAlchemy can be useful for complex queries
DATABASE_URL = f"postgresql://postgres:{settings.supabase_service_key}@{settings.supabase_url.replace('https://', '').replace('http://', '')}/postgres"

try:
    engine = create_engine(DATABASE_URL, echo=settings.debug)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    Base = declarative_base()
    metadata = MetaData()
except Exception as e:
    logger.warning(f"SQLAlchemy setup failed: {e}. Using Supabase client only.")
    engine = None
    SessionLocal = None
    Base = None
    metadata = None


def get_supabase_client() -> Client:
    """Get Supabase client instance."""
    return supabase


def get_db_session():
    """Get database session (SQLAlchemy)."""
    if SessionLocal is None:
        raise RuntimeError("Database session not available. Check database configuration.")
    
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


class DatabaseManager:
    """Database operations manager using Supabase."""
    
    def __init__(self):
        self.client = supabase
    
    async def create_profile(self, user_data: dict) -> dict:
        """Create a new user profile."""
        try:
            result = self.client.table('profiles').insert(user_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating profile: {e}")
            raise
    
    async def get_profile(self, user_id: str) -> dict:
        """Get user profile by ID."""
        try:
            result = self.client.table('profiles').select('*').eq('id', user_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting profile: {e}")
            raise
    
    async def update_profile(self, user_id: str, update_data: dict) -> dict:
        """Update user profile."""
        try:
            result = self.client.table('profiles').update(update_data).eq('id', user_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error updating profile: {e}")
            raise
    
    async def create_question(self, question_data: dict) -> dict:
        """Create a new question."""
        try:
            result = self.client.table('questions').insert(question_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating question: {e}")
            raise
    
    async def get_questions(self, filters: dict = None, limit: int = 50, offset: int = 0) -> list:
        """Get questions with optional filters."""
        try:
            query = self.client.table('questions').select('*')
            
            if filters:
                for key, value in filters.items():
                    query = query.eq(key, value)
            
            result = query.range(offset, offset + limit - 1).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting questions: {e}")
            raise
    
    async def get_question(self, question_id: str) -> dict:
        """Get a specific question by ID."""
        try:
            result = self.client.table('questions').select('*').eq('id', question_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting question: {e}")
            raise
    
    async def create_submission(self, submission_data: dict) -> dict:
        """Create a new code submission."""
        try:
            result = self.client.table('submissions').insert(submission_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating submission: {e}")
            raise
    
    async def get_submission(self, submission_id: str) -> dict:
        """Get a specific submission by ID."""
        try:
            result = self.client.table('submissions').select('*').eq('id', submission_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting submission: {e}")
            raise

    async def update_submission(self, submission_id: str, update_data: dict) -> dict:
        """Update submission record."""
        try:
            result = self.client.table('submissions').update(update_data).eq('id', submission_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error updating submission: {e}")
            raise

    async def get_submissions(self, user_id: str, limit: int = 50, offset: int = 0) -> list:
        """Get user submissions."""
        try:
            result = self.client.table('submissions').select('*').eq('user_id', user_id).range(offset, offset + limit - 1).order('created_at', desc=True).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting submissions: {e}")
            raise
    
    async def create_file_upload(self, file_data: dict) -> dict:
        """Create a file upload record."""
        try:
            result = self.client.table('file_uploads').insert(file_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating file upload: {e}")
            raise
    
    async def get_file_upload(self, file_id: str) -> dict:
        """Get file upload record by ID."""
        try:
            result = self.client.table('file_uploads').select('*').eq('id', file_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting file upload: {e}")
            raise

    async def get_user_files(self, user_id: str, limit: int = 50, offset: int = 0) -> list:
        """Get user's uploaded files."""
        try:
            result = self.client.table('file_uploads').select('*').eq('user_id', user_id).range(offset, offset + limit - 1).order('created_at', desc=True).execute()
            return result.data or []
        except Exception as e:
            logger.error(f"Error getting user files: {e}")
            raise

    async def delete_file_upload(self, file_id: str) -> bool:
        """Delete file upload record."""
        try:
            result = self.client.table('file_uploads').delete().eq('id', file_id).execute()
            return len(result.data) > 0
        except Exception as e:
            logger.error(f"Error deleting file upload: {e}")
            raise

    async def update_file_upload(self, file_id: str, update_data: dict) -> dict:
        """Update file upload record."""
        try:
            result = self.client.table('file_uploads').update(update_data).eq('id', file_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error updating file upload: {e}")
            raise
    
    async def create_question_job(self, job_data: dict) -> dict:
        """Create a question generation job."""
        try:
            result = self.client.table('question_jobs').insert(job_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error creating question job: {e}")
            raise
    
    async def get_question_job(self, job_id: str) -> dict:
        """Get question generation job by ID."""
        try:
            result = self.client.table('question_jobs').select('*').eq('id', job_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error getting question job: {e}")
            raise

    async def delete_question(self, question_id: str) -> bool:
        """Delete question record."""
        try:
            result = self.client.table('questions').delete().eq('id', question_id).execute()
            return len(result.data) > 0
        except Exception as e:
            logger.error(f"Error deleting question: {e}")
            raise

    async def update_question_job(self, job_id: str, update_data: dict) -> dict:
        """Update question generation job."""
        try:
            result = self.client.table('question_jobs').update(update_data).eq('id', job_id).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error updating question job: {e}")
            raise
    
    async def log_activity(self, log_data: dict) -> dict:
        """Log user activity."""
        try:
            result = self.client.table('logs').insert(log_data).execute()
            return result.data[0] if result.data else None
        except Exception as e:
            logger.error(f"Error logging activity: {e}")
            # Don't raise for logging errors to avoid breaking main functionality
            return None


# Global database manager instance
db_manager = DatabaseManager()
