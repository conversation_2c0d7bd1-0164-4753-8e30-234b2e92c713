"""
File processing and text extraction service for AI-Examiner.
"""
import os
import uuid
from io import BytesIO
from typing import Optional, <PERSON><PERSON>
import aiofiles
import pdfplumber
import docx
from PIL import Image
import pytesseract
import openpyxl
import magic
from fastapi import HTTPException, UploadFile
from app.config import settings, FILE_TYPE_MAPPINGS
from app.database import db_manager, supabase
import logging

logger = logging.getLogger(__name__)


class FileProcessor:
    """File processing service for handling uploads and text extraction."""
    
    def __init__(self):
        self.db = db_manager
        self.upload_dir = "uploads"
        self.max_file_size = settings.max_file_size
        self.allowed_extensions = settings.allowed_extensions
        
        # Create upload directory if it doesn't exist
        os.makedirs(self.upload_dir, exist_ok=True)
    
    def _validate_file(self, file: UploadFile) -> Tuple[bool, str]:
        """Validate uploaded file."""
        # Check file size
        if hasattr(file, 'size') and file.size > self.max_file_size:
            return False, f"File size exceeds maximum allowed size of {self.max_file_size} bytes"
        
        # Check file extension
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in self.allowed_extensions:
            return False, f"File type {file_ext} not allowed. Allowed types: {', '.join(self.allowed_extensions)}"
        
        return True, "Valid file"
    
    def _detect_file_type(self, file_content: bytes, filename: str) -> str:
        """Detect file type using python-magic."""
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            return FILE_TYPE_MAPPINGS.get(mime_type, os.path.splitext(filename)[1][1:].lower())
        except Exception as e:
            logger.warning(f"Could not detect MIME type: {e}")
            return os.path.splitext(filename)[1][1:].lower()
    
    async def upload_file(self, file: UploadFile, user_id: str) -> dict:
        """Upload file and create database record."""
        try:
            # Validate file
            is_valid, message = self._validate_file(file)
            if not is_valid:
                raise HTTPException(status_code=400, detail=message)
            
            # Read file content
            file_content = await file.read()
            file_size = len(file_content)
            
            # Generate unique filename
            file_id = str(uuid.uuid4())
            file_ext = os.path.splitext(file.filename)[1]
            unique_filename = f"{file_id}{file_ext}"
            file_path = os.path.join(self.upload_dir, unique_filename)
            
            # Detect file type
            file_type = self._detect_file_type(file_content, file.filename)
            
            # Save file to local storage
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            # Upload to Supabase Storage (optional)
            try:
                storage_response = supabase.storage.from_('uploads').upload(
                    unique_filename, file_content
                )
                storage_path = f"uploads/{unique_filename}"
            except Exception as e:
                logger.warning(f"Failed to upload to Supabase Storage: {e}")
                storage_path = file_path
            
            # Create database record
            file_data = {
                "id": file_id,
                "user_id": user_id,
                "file_name": file.filename,
                "file_type": file_type,
                "file_size": file_size,
                "file_path": storage_path,
                "processing_status": "pending"
            }
            
            file_record = await self.db.create_file_upload(file_data)
            
            # Log file upload activity
            await self.db.log_activity({
                "user_id": user_id,
                "event_type": "file_upload",
                "event_data": {
                    "file_name": file.filename,
                    "file_type": file_type,
                    "file_size": file_size
                }
            })
            
            return file_record
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"File upload error: {e}")
            raise HTTPException(status_code=500, detail="File upload failed")
    
    def extract_text_from_docx(self, file_content: bytes) -> str:
        """Extract text from DOCX file."""
        try:
            doc = docx.Document(BytesIO(file_content))
            text_parts = []
            
            # Extract text from paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_parts.append(cell.text.strip())
            
            return "\n".join(text_parts)
            
        except Exception as e:
            logger.error(f"DOCX extraction error: {e}")
            raise Exception(f"Failed to extract text from DOCX: {e}")
    
    def extract_text_from_pdf(self, file_content: bytes) -> str:
        """Extract text from PDF file."""
        try:
            text_parts = []
            
            with pdfplumber.open(BytesIO(file_content)) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(page_text.strip())
            
            return "\n".join(text_parts)
            
        except Exception as e:
            logger.error(f"PDF extraction error: {e}")
            raise Exception(f"Failed to extract text from PDF: {e}")
    
    def extract_text_from_image(self, file_content: bytes) -> str:
        """Extract text from image using OCR."""
        try:
            image = Image.open(BytesIO(file_content))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Extract text using Tesseract OCR
            text = pytesseract.image_to_string(image, lang='eng')
            return text.strip()
            
        except Exception as e:
            logger.error(f"Image OCR error: {e}")
            raise Exception(f"Failed to extract text from image: {e}")
    
    def extract_text_from_xlsx(self, file_content: bytes) -> str:
        """Extract text from Excel file."""
        try:
            workbook = openpyxl.load_workbook(BytesIO(file_content), data_only=True)
            text_parts = []
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text_parts.append(f"Sheet: {sheet_name}")
                
                for row in sheet.iter_rows(values_only=True):
                    row_text = []
                    for cell_value in row:
                        if cell_value is not None:
                            row_text.append(str(cell_value))
                    
                    if row_text:
                        text_parts.append(" | ".join(row_text))
            
            return "\n".join(text_parts)
            
        except Exception as e:
            logger.error(f"Excel extraction error: {e}")
            raise Exception(f"Failed to extract text from Excel: {e}")
    
    def extract_text_from_txt(self, file_content: bytes) -> str:
        """Extract text from plain text file."""
        try:
            # Try different encodings
            encodings = ['utf-8', 'utf-16', 'latin-1', 'cp1252']
            
            for encoding in encodings:
                try:
                    return file_content.decode(encoding).strip()
                except UnicodeDecodeError:
                    continue
            
            # If all encodings fail, use utf-8 with error handling
            return file_content.decode('utf-8', errors='replace').strip()
            
        except Exception as e:
            logger.error(f"Text extraction error: {e}")
            raise Exception(f"Failed to extract text from file: {e}")
    
    async def extract_text(self, file_id: str) -> str:
        """Extract text from uploaded file."""
        try:
            # Update processing status
            await self.db.update_file_upload(file_id, {"processing_status": "processing"})
            
            # Get file record
            file_record = await self.db.get_file_upload(file_id)
            if not file_record:
                raise Exception("File record not found")
            
            # Read file content
            file_path = file_record['file_path']
            if file_path.startswith('uploads/'):
                # Download from Supabase Storage
                try:
                    file_content = supabase.storage.from_('uploads').download(
                        file_path.replace('uploads/', '')
                    )
                except Exception as e:
                    logger.warning(f"Failed to download from Supabase Storage: {e}")
                    # Fallback to local file
                    async with aiofiles.open(file_path, 'rb') as f:
                        file_content = await f.read()
            else:
                # Read from local file
                async with aiofiles.open(file_path, 'rb') as f:
                    file_content = await f.read()
            
            # Extract text based on file type
            file_type = file_record['file_type'].lower()
            
            if file_type == 'pdf':
                extracted_text = self.extract_text_from_pdf(file_content)
            elif file_type in ['docx', 'doc']:
                extracted_text = self.extract_text_from_docx(file_content)
            elif file_type in ['xlsx', 'xls']:
                extracted_text = self.extract_text_from_xlsx(file_content)
            elif file_type in ['png', 'jpg', 'jpeg', 'gif']:
                extracted_text = self.extract_text_from_image(file_content)
            elif file_type == 'txt':
                extracted_text = self.extract_text_from_txt(file_content)
            else:
                raise Exception(f"Unsupported file type: {file_type}")
            
            # Update file record with extracted text
            await self.db.update_file_upload(file_id, {
                "extracted_text": extracted_text,
                "processing_status": "completed"
            })
            
            # Log text extraction activity
            await self.db.log_activity({
                "user_id": file_record['user_id'],
                "event_type": "text_extraction",
                "event_data": {
                    "file_id": file_id,
                    "file_name": file_record['file_name'],
                    "text_length": len(extracted_text)
                }
            })
            
            return extracted_text
            
        except Exception as e:
            logger.error(f"Text extraction error: {e}")
            
            # Update processing status to failed
            await self.db.update_file_upload(file_id, {
                "processing_status": "failed",
                "error_message": str(e)
            })
            
            raise Exception(f"Text extraction failed: {e}")


# Global file processor instance
file_processor = FileProcessor()
