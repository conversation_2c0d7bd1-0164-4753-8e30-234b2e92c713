# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo

# Judge0 Configuration
JUDGE0_API_KEY=your_rapidapi_key
JUDGE0_HOST=judge0-ce.p.rapidapi.com
JUDGE0_URL=https://judge0-ce.p.rapidapi.com

# Application Configuration
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_EXTENSIONS=.pdf,.docx,.doc,.txt,.xlsx,.xls,.png,.jpg,.jpeg,.gif

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Development Configuration
DEBUG=True
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000", "http://127.0.0.1:8000"]

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=app.log
