# AI-Examer — Full-Stack Blueprint & Code

> Complete design, code snippets and implementation plan to build the AI-Examer platform described.

---

## 1. Project Overview

A web application that accepts file uploads (.docx, .pdf, txt, xlsx, images), extracts text, generates different question types (coding problems, MCQs, aptitude), lets users write/compile code in-browser across languages (Java, Python, C, C++, JavaScript, HTML), evaluates code (compile/run), provides complexity/space/size estimates, gives suggestions & error corrections, and handles authentication & logging via Supabase. Backend orchestrates file parsing, OpenAI prompt calls, and job submission to a code-execution engine (Judge0 or self-hosted runner).

---

## 2. Tech Stack

* Frontend: HTML, JavaScript (vanilla or React), Tailwind optional
* Backend: Python + FastAPI
* Authentication & DB: Supabase (Postgres + Auth)
* LLM: OpenAI (Chat Completions or Responses API)
* Code execution: Judge0 public API or self-hosted Judge0 for security
* File processing: python-docx, pdfplumber, tika (optional), pillow for images + OCR (pytesseract)
* Storage: Supabase Storage (or S3)

---

## 3. High-level Architecture

1. User uploads file via frontend -> file stored in Supabase Storage -> backend receives webhook or fetches the file.
2. Backend extracts text using appropriate parser.
3. Backend calls OpenAI with prompt templates to generate questions (MCQ, coding, aptitude).
4. Generated items stored in `questions` table.
5. User picks a question -> opens editor -> sends code to backend which forwards to Judge0.
6. Judge0 returns execution results -> backend asks OpenAI for code review, complexity estimate, and suggestions -> results shown to user.

---

## 4. Database Schema (Postgres / Supabase)

```sql
-- users table: extra fields stored in profiles table in Supabase
CREATE TABLE profiles (
  id uuid PRIMARY KEY,
  first_name text,
  last_name text,
  email text UNIQUE,
  dob date,
  phone text,
  unique_user_id text UNIQUE, -- custom UID
  created_at timestamptz DEFAULT now()
);

CREATE TABLE questions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  source_file text,
  type text, -- 'mcq'|'code'|'aptitude'
  prompt jsonb,
  difficulty text,
  created_by uuid REFERENCES profiles(id),
  created_at timestamptz DEFAULT now()
);

CREATE TABLE submissions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id),
  question_id uuid REFERENCES questions(id),
  language text,
  code text,
  result jsonb,
  score int,
  created_at timestamptz DEFAULT now()
);

CREATE TABLE logs (
  id serial PRIMARY KEY,
  user_id uuid,
  event_type text,
  event_data jsonb,
  created_at timestamptz DEFAULT now()
);
```

---

## 5. Supabase Auth & Registration Rules

* Use Supabase Auth for sign-up/login (email + password).
* Additionally create `profiles` row on `auth:signup` webhook.
* Password policy (8-15 chars): must include lowercase, uppercase, digit, special char.

**Password validation regex (python):**

```py
import re
PATTERN = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,15}$'
re.match(PATTERN, password)
```

**Unique ID generation:**

* Format: first 4 characters of name (pad with X if <4) + YYYY (year of DOB) + 2 uppercase letters + 3-digit number
* Example: `CHAR1998AZ123`

**UID generator (python):**

```py
import random
import string

def generate_uid(first_name, dob):
    name_part = (first_name[:4].upper() + 'XXXX')[:4]
    year = dob.year
    letters = ''.join(random.choices(string.ascii_uppercase, k=2))
    digits = f"{random.randint(0,999):03d}"
    return f"{name_part}{year}{letters}{digits}"
```

---

## 6. File extraction (Python examples)

**Requirements:** `python-docx`, `pdfplumber`, `pytesseract`, `Pillow`.

```py
# file_parser.py
from io import BytesIO
import pdfplumber
import docx
from PIL import Image
import pytesseract

def extract_text_from_docx(bytes_data):
    doc = docx.Document(BytesIO(bytes_data))
    return "\n".join(p.text for p in doc.paragraphs)

def extract_text_from_pdf(bytes_data):
    text = []
    with pdfplumber.open(BytesIO(bytes_data)) as pdf:
        for page in pdf.pages:
            text.append(page.extract_text() or "")
    return "\n".join(text)

def extract_text_from_image(bytes_data):
    img = Image.open(BytesIO(bytes_data))
    return pytesseract.image_to_string(img)
```

**Notes:** for `.doc` or obscure formats use Apache Tika or convert to docx.

---

## 7. Question-generation (OpenAI prompt templates)

**MCQ generation prompt (example):**

```
Extract 5 multiple-choice questions from the following text. For each question, provide: question, 4 options labeled A-D, correct option letter, a short explanation (1-2 sentences), and difficulty (easy/medium/hard).
Text:
"""
{extracted_text}
"""
Format output as JSON array.
```

**Coding problem generation prompt (example):**

```
From the following passage, generate 3 coding problems suitable for an interview or exam. Each problem should include: title, detailed problem statement, input/output format, constraints, sample input, sample output, and difficulty.
Text:
{extracted_text}
```

**Aptitude question prompt (example):** similar structure.

---

## 8. Code execution (Judge0 integration)

**Why Judge0:** easy to integrate via REST API; supports many languages; sandboxed.

**Sample submit to Judge0 (Python snippet):**

```py
import requests

JUDGE0_URL = 'https://judge0-ce.p.rapidapi.com/submissions'  # or your self-hosted
API_HOST = 'judge0-ce.p.rapidapi.com'
API_KEY = 'YOUR_RAPIDAPI_KEY'

def submit_to_judge0(source_code, language_id, stdin=''):
    headers = {
        'x-rapidapi-host': API_HOST,
        'x-rapidapi-key': API_KEY,
        'content-type': 'application/json'
    }
    body = {"source_code": source_code, "language_id": language_id, "stdin": stdin}
    r = requests.post(JUDGE0_URL + '?base64_encoded=false&wait=true', headers=headers, json=body)
    return r.json()
```

**Language IDs:** map languages to Judge0 ids (Python, Java, C, C++, JS, etc.).

---

## 9. Complexity, size, space & standards evaluation

We can achieve this via a two-step approach:

1. **Static heuristics:** run simple analysers (count nested loops, recursion use, big-O heuristics via AST) for Python/JS. This gives a rough estimate.
2. **LLM-based reasoning:** send the user's code + short context to OpenAI asking it to estimate time complexity, space complexity, code size, styling and comments, and return suggested improvements.

**Example prompt to OpenAI for analysis:**

```
You are an expert software engineer. Analyze the following code and provide:
1. Time complexity (Big-O) with reasoning.
2. Space complexity.
3. Code size (approx bytes and lines).
4. Where comments are missing or unclear.
5. Suggest improvements, edge-case issues, and security concerns.
Code:
"""
{user_code}
"""
Language: {language}
Output: JSON with fields: time_complexity, space_complexity, lines, bytes, suggested_changes, corrected_code (if simple fixes possible).
```

**Caveat:** LLM estimates are heuristic — clearly tell users it's advisory.

---

## 10. Error correction & suggestion pipeline

1. Receive compilation/runtime error from Judge0.
2. Construct prompt with error message + code + description asking OpenAI to propose fixes and explain root cause.
3. Optionally auto-propose a patch and run patched code in Judge0 (with user opt-in).

**Example:**

```
System: You are a debugging assistant.
User: I ran this code and got this error: {error_message}
Code: {code}
Task: Provide root cause, 2-3 possible fixes, and a patched version if trivial.
```

---

## 11. Logging & Audit

* Log sign-ins/outs, file uploads, question generation events, submissions, evaluation results, and corrections into `logs` table.
* Keep `submissions` results as JSON for reproducible auditing.

---

## 12. API endpoints (FastAPI example)

```
POST /upload-file -> receive file, store in Supabase Storage, start text extraction job, returns job_id
GET /job/{job_id} -> check extraction/generation status
POST /generate-questions -> trigger question generation from extracted text
GET /questions/{id}
POST /submit-code -> send code to Judge0, await result, ask OpenAI for analysis, return combined result
POST /auth/register -> optional if you want backend create (recommended: use Supabase Auth directly from frontend)
```

**Basic FastAPI skeleton:**

```py
from fastapi import FastAPI, File, UploadFile
app = FastAPI()

@app.post('/upload-file')
async def upload_file(file: UploadFile = File(...)):
    contents = await file.read()
    # store in supabase-storage, kick off extraction + question gen
    return {"status": "ok"}
```

---

## 13. Frontend — Login / Register (HTML + JS using Supabase JS)

**Install:** `npm i @supabase/supabase-js`

```html
<!-- register.html (snippet) -->
<form id="register">
  <input id="first_name" />
  <input id="last_name" />
  <input id="email" />
  <input id="dob" type="date" />
  <input id="phone" />
  <input id="password" type="password" />
  <button type="submit">Register</button>
</form>

<script type="module">
import { createClient } from '@supabase/supabase-js'
const supabase = createClient('https://your-supabase-url', 'PUBLIC_ANON_KEY')

document.getElementById('register').addEventListener('submit', async e => {
  e.preventDefault()
  const first_name = document.getElementById('first_name').value
  const email = document.getElementById('email').value
  const password = document.getElementById('password').value
  // client-side password validation
  const PATTERN = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,15}$/
  if (!PATTERN.test(password)) return alert('Password rules...')
  const { data, error } = await supabase.auth.signUp({ email, password })
  if (error) return alert(error.message)
  // call backend to create profile and generate unique_user_id
})
</script>
```

**Note:** always use server-side verification for security.

---

## 14. Example: Code review / analysis flow

1. User submits code -> backend stores submission -> backend calls Judge0 -> receives `stdout`, `stderr`, `time`, `memory`.
2. Backend constructs analysis prompt including runtime metrics and sends to OpenAI for suggestions and complexity estimate.
3. Save combined response in `submissions.result`.

---

## 15. Security & Data Privacy Notes

* Never send sensitive data to public judge instances; consider self-hosting Judge0 for private exams.
* Rate-limit OpenAI and Judge0 calls.
* Sanitize uploaded files (scan for malicious content).
* Enforce CORS, HTTPS, and server-side validation.

---

## 16. Deployment & Cost considerations

* Supabase has generous free tier for testing, but file storage and edge functions may incur cost.
* OpenAI usage costs depend on tokens; batch question generation helps lower calls.
* Judge0 public endpoints may have rate limits—consider self-hosting.

---

## 17. Next steps & Deliverables I can produce for you now

* Full repository skeleton (backend FastAPI app, frontend HTML/React, DB migration SQL).
* Working FastAPI endpoint to upload a file and extract text.
* Question-generation service using OpenAI prompt templates and returning JSON.
* Judge0 integration example and frontend code runner.
* Complete register/login pages wired to Supabase.

If you want, I can generate the full repo file-by-file (frontend + backend) in this canvas as code files or produce downloadable files (zip). Tell me which deliverable you want first and I will create it.

---

*End of blueprint.*

+-------------------+
|       User        |
+-------------------+
         |
         v
+-------------------+
|   Login / Register|
+-------------------+
         |
         v
+-------------------+
|  Supabase Auth    |
+-------------------+
         |
         v
+-------------------+
|    Upload File    |
+-------------------+
         |
         v
+-------------------+
| Supabase Storage  |
+-------------------+
         |
         v
+-------------------+
|  Text Extraction  |
+-------------------+
         |
         v
+-----------------------------+
| OpenAI Question Generation  |
+-----------------------------+
         |
         v
+-------------------+
|   Questions DB    |
+-------------------+
         |
         v
+-------------------+
|  Select Question  |
+-------------------+
         |
         v
+-------------------+
|    Code Editor    |
+-------------------+
         |
         v
+-------------------+
|   Judge0 API      |
+-------------------+
         |
         v
+-------------------+
| Execution Result  |
+-------------------+
         |
         v
+-------------------+
| OpenAI Analysis   |
+-------------------+
         |
         v
+-----------------------------+
| Suggestions / Corrections   |
+-----------------------------+
         |
         v
+-------------------+    +-------------------+
| Submissions DB    |    |     Logs DB       |
+-------------------+    +-------------------+

