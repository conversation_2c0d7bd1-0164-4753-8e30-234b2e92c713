"""
Question models and schemas for AI-Examiner.
"""
from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, validator
from app.config import QUESTION_TYPES, DIFFICULTY_LEVELS


class MCQOption(BaseModel):
    """Multiple choice question option."""
    label: str  # A, B, C, D
    text: str


class MCQContent(BaseModel):
    """MCQ question content."""
    question: str
    options: List[MCQOption]
    correct_option: str
    explanation: str


class CodingContent(BaseModel):
    """Coding question content."""
    problem_statement: str
    input_format: str
    output_format: str
    constraints: List[str]
    sample_input: str
    sample_output: str
    test_cases: Optional[List[Dict[str, str]]] = []


class AptitudeContent(BaseModel):
    """Aptitude question content."""
    question: str
    options: Optional[List[MCQOption]] = None
    correct_answer: str
    explanation: str
    topic: Optional[str] = None


class QuestionCreate(BaseModel):
    """Question creation schema."""
    source_file: str
    file_name: str
    type: str
    title: str
    content: Dict[str, Any]
    difficulty: str
    created_by: str
    
    @validator('type')
    def validate_type(cls, v):
        if v not in QUESTION_TYPES:
            raise ValueError(f'Type must be one of: {", ".join(QUESTION_TYPES)}')
        return v
    
    @validator('difficulty')
    def validate_difficulty(cls, v):
        if v not in DIFFICULTY_LEVELS:
            raise ValueError(f'Difficulty must be one of: {", ".join(DIFFICULTY_LEVELS)}')
        return v


class Question(BaseModel):
    """Question schema."""
    id: str
    source_file: str
    file_name: str
    type: str
    title: str
    content: Dict[str, Any]
    difficulty: str
    created_by: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class QuestionGenerationRequest(BaseModel):
    """Question generation request schema."""
    file_id: str
    question_types: List[str] = ["mcq", "code", "aptitude"]
    count_per_type: int = 5
    difficulty_distribution: Dict[str, int] = {"easy": 2, "medium": 2, "hard": 1}
    
    @validator('question_types')
    def validate_question_types(cls, v):
        for qtype in v:
            if qtype not in QUESTION_TYPES:
                raise ValueError(f'Invalid question type: {qtype}')
        return v
    
    @validator('count_per_type')
    def validate_count(cls, v):
        if v < 1 or v > 10:
            raise ValueError('Count per type must be between 1 and 10')
        return v


class QuestionGenerationJob(BaseModel):
    """Question generation job schema."""
    id: str
    file_upload_id: str
    user_id: str
    job_status: str
    question_types: List[str]
    generated_count: int
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class QuestionGenerationResponse(BaseModel):
    """Question generation response schema."""
    job_id: str
    status: str
    message: str
    questions: Optional[List[Question]] = None
