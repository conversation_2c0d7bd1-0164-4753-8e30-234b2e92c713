<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AI-Examiner</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <header class="header">
        <div class="container clearfix">
            <h1><a href="index.html" style="color: white; text-decoration: none;">AI-Examiner</a></h1>
            <nav class="nav">
                <a href="index.html">Home</a>
                <a href="register.html">Register</a>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <div class="row">
                <div class="col-2" style="margin: 0 auto;">
                    <div class="card">
                        <div class="card-header text-center">
                            <h2 class="card-title">Login to Your Account</h2>
                            <p style="color: #666;">Welcome back! Please sign in to continue.</p>
                        </div>

                        <div id="alert-container"></div>

                        <form id="login-form">
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    class="form-input" 
                                    required 
                                    placeholder="Enter your email"
                                >
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">Password</label>
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    class="form-input" 
                                    required 
                                    placeholder="Enter your password"
                                >
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary" style="width: 100%;">
                                    <span id="login-btn-text">Sign In</span>
                                    <div id="login-spinner" class="spinner" style="display: none; width: 20px; height: 20px; margin: 0 auto;"></div>
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-3">
                            <p>Don't have an account? <a href="register.html" style="color: #667eea; text-decoration: none;">Sign up here</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 AI-Examiner. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const alertContainer = document.getElementById('alert-container');
            const loginBtnText = document.getElementById('login-btn-text');
            const loginSpinner = document.getElementById('login-spinner');

            function showAlert(message, type = 'error') {
                alertContainer.innerHTML = `
                    <div class="alert alert-${type}">
                        ${message}
                    </div>
                `;
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }

            function setLoading(loading) {
                if (loading) {
                    loginBtnText.style.display = 'none';
                    loginSpinner.style.display = 'block';
                    loginForm.querySelector('button').disabled = true;
                } else {
                    loginBtnText.style.display = 'inline';
                    loginSpinner.style.display = 'none';
                    loginForm.querySelector('button').disabled = false;
                }
            }

            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(loginForm);
                const email = formData.get('email');
                const password = formData.get('password');

                if (!email || !password) {
                    showAlert('Please fill in all fields.');
                    return;
                }

                setLoading(true);

                try {
                    const result = await authService.login(email, password);
                    
                    if (result.success) {
                        showAlert('Login successful! Redirecting...', 'success');
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1000);
                    } else {
                        showAlert(result.error || 'Login failed. Please try again.');
                    }
                } catch (error) {
                    showAlert('An error occurred. Please try again.');
                    console.error('Login error:', error);
                } finally {
                    setLoading(false);
                }
            });

            // Auto-focus email field
            document.getElementById('email').focus();
        });
    </script>
</body>
</html>
