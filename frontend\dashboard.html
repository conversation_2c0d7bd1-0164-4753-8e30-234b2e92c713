<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AI-Examiner</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <header class="header">
        <div class="container clearfix">
            <h1><a href="dashboard.html" style="color: white; text-decoration: none;">AI-Examiner</a></h1>
            <nav class="nav">
                <span>Welcome, <span data-user-name>User</span>!</span>
                <a href="#" onclick="authService.logout()">Logout</a>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <div id="alert-container"></div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Quick Actions</h2>
                </div>
                <div class="row">
                    <div class="col-4">
                        <div class="text-center">
                            <div style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;">📄</div>
                            <h4>Upload Document</h4>
                            <p>Upload a file to extract text and generate questions</p>
                            <button class="btn btn-primary" onclick="showFileUpload()">Upload File</button>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <div style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;">❓</div>
                            <h4>Browse Questions</h4>
                            <p>View and manage your generated questions</p>
                            <button class="btn btn-primary" onclick="showQuestions()">View Questions</button>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="text-center">
                            <div style="font-size: 3rem; color: #667eea; margin-bottom: 1rem;">💻</div>
                            <h4>Code Editor</h4>
                            <p>Write and test code with AI analysis</p>
                            <button class="btn btn-primary" onclick="showCodeEditor()">Open Editor</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Upload Section -->
            <div id="file-upload-section" class="card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">Upload Document</h2>
                </div>
                <div class="file-upload" id="file-drop-zone">
                    <div style="font-size: 3rem; margin-bottom: 1rem;">📁</div>
                    <h3>Drop your file here or click to browse</h3>
                    <p>Supported formats: PDF, DOCX, TXT, XLSX, Images</p>
                    <input type="file" id="file-input" style="display: none;" accept=".pdf,.docx,.doc,.txt,.xlsx,.xls,.png,.jpg,.jpeg,.gif">
                    <button class="btn btn-outline" onclick="document.getElementById('file-input').click()">
                        Choose File
                    </button>
                </div>
                <div id="upload-progress" style="display: none;">
                    <div class="spinner"></div>
                    <p>Uploading and processing file...</p>
                </div>
            </div>

            <!-- Questions Section -->
            <div id="questions-section" class="card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">Your Questions</h2>
                    <button class="btn btn-secondary" onclick="loadQuestions()">Refresh</button>
                </div>
                <div id="questions-list">
                    <div class="spinner"></div>
                    <p>Loading questions...</p>
                </div>
            </div>

            <!-- Code Editor Section -->
            <div id="code-editor-section" class="card" style="display: none;">
                <div class="card-header">
                    <h2 class="card-title">Code Editor</h2>
                    <div>
                        <select id="language-select" class="form-select" style="width: auto; display: inline-block; margin-right: 1rem;">
                            <option value="python">Python</option>
                            <option value="java">Java</option>
                            <option value="cpp">C++</option>
                            <option value="c">C</option>
                            <option value="javascript">JavaScript</option>
                        </select>
                        <button class="btn btn-success" onclick="runCode()">Run Code</button>
                        <button class="btn btn-secondary" onclick="analyzeCode()">Analyze</button>
                    </div>
                </div>
                <div class="row">
                    <div class="col-2">
                        <textarea id="code-input" class="form-textarea" placeholder="Write your code here..." style="height: 300px; font-family: 'Courier New', monospace;"></textarea>
                    </div>
                    <div class="col-2">
                        <div style="background-color: #f8f9fa; padding: 1rem; border-radius: 5px; height: 300px; overflow-y: auto;">
                            <h4>Output:</h4>
                            <pre id="code-output" style="white-space: pre-wrap; font-family: 'Courier New', monospace;"></pre>
                        </div>
                    </div>
                </div>
                <div id="code-analysis" style="display: none; margin-top: 1rem;">
                    <h4>AI Analysis:</h4>
                    <div id="analysis-content"></div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">Recent Activity</h2>
                </div>
                <div id="recent-activity">
                    <p>Loading recent activity...</p>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 AI-Examiner. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>

    <script>
        let currentSection = null;

        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alert-container');
            alertContainer.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }

        function hideAllSections() {
            document.getElementById('file-upload-section').style.display = 'none';
            document.getElementById('questions-section').style.display = 'none';
            document.getElementById('code-editor-section').style.display = 'none';
        }

        function showFileUpload() {
            hideAllSections();
            document.getElementById('file-upload-section').style.display = 'block';
            currentSection = 'upload';
        }

        function showQuestions() {
            hideAllSections();
            document.getElementById('questions-section').style.display = 'block';
            currentSection = 'questions';
            loadQuestions();
        }

        function showCodeEditor() {
            hideAllSections();
            document.getElementById('code-editor-section').style.display = 'block';
            currentSection = 'editor';
        }

        async function loadQuestions() {
            try {
                const questions = await apiService.getMyQuestions();
                const questionsList = document.getElementById('questions-list');
                
                if (questions.length === 0) {
                    questionsList.innerHTML = '<p>No questions found. Upload a document to generate questions.</p>';
                    return;
                }

                questionsList.innerHTML = questions.map(q => `
                    <div class="question-card card" style="margin-bottom: 1rem;">
                        <h4>${q.title}</h4>
                        <div>
                            <span class="question-type ${q.type}">${q.type.toUpperCase()}</span>
                            <span class="difficulty ${q.difficulty}">${q.difficulty}</span>
                        </div>
                        <p style="margin-top: 0.5rem; color: #666;">From: ${q.file_name}</p>
                    </div>
                `).join('');
            } catch (error) {
                showAlert('Failed to load questions: ' + error.message, 'error');
            }
        }

        async function runCode() {
            const code = document.getElementById('code-input').value;
            const language = document.getElementById('language-select').value;
            const output = document.getElementById('code-output');

            if (!code.trim()) {
                showAlert('Please enter some code to run.', 'warning');
                return;
            }

            output.textContent = 'Running code...';

            try {
                const result = await apiService.submitCode({
                    question_id: 'demo', // Demo submission
                    language: language,
                    code: code,
                    input_data: ''
                });

                output.textContent = result.execution_result.stdout || result.execution_result.stderr || 'No output';
                
                if (result.analysis) {
                    showCodeAnalysis(result.analysis);
                }

                showAlert('Code executed successfully!', 'success');
            } catch (error) {
                output.textContent = 'Error: ' + error.message;
                showAlert('Code execution failed: ' + error.message, 'error');
            }
        }

        async function analyzeCode() {
            const code = document.getElementById('code-input').value;
            const language = document.getElementById('language-select').value;

            if (!code.trim()) {
                showAlert('Please enter some code to analyze.', 'warning');
                return;
            }

            try {
                const result = await apiService.getCodeSuggestions(code, language);
                showCodeAnalysis({ suggestions: result.suggestions });
                showAlert('Code analysis completed!', 'success');
            } catch (error) {
                showAlert('Code analysis failed: ' + error.message, 'error');
            }
        }

        function showCodeAnalysis(analysis) {
            const analysisSection = document.getElementById('code-analysis');
            const analysisContent = document.getElementById('analysis-content');
            
            let content = '';
            
            if (analysis.time_complexity) {
                content += `<p><strong>Time Complexity:</strong> ${analysis.time_complexity}</p>`;
            }
            
            if (analysis.space_complexity) {
                content += `<p><strong>Space Complexity:</strong> ${analysis.space_complexity}</p>`;
            }
            
            if (analysis.suggestions && analysis.suggestions.length > 0) {
                content += '<p><strong>Suggestions:</strong></p><ul>';
                analysis.suggestions.forEach(suggestion => {
                    content += `<li>${suggestion}</li>`;
                });
                content += '</ul>';
            }
            
            analysisContent.innerHTML = content;
            analysisSection.style.display = 'block';
        }

        // File upload handling
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('file-input');
            const dropZone = document.getElementById('file-drop-zone');

            fileInput.addEventListener('change', handleFileUpload);

            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload({ target: { files } });
                }
            });

            async function handleFileUpload(event) {
                const file = event.target.files[0];
                if (!file) return;

                const progressDiv = document.getElementById('upload-progress');
                progressDiv.style.display = 'block';

                try {
                    const result = await apiService.uploadFile(file);
                    showAlert('File uploaded successfully!', 'success');
                    
                    // Start text extraction
                    await apiService.extractText(result.file_id);
                    showAlert('Text extraction completed!', 'success');
                    
                } catch (error) {
                    showAlert('File upload failed: ' + error.message, 'error');
                } finally {
                    progressDiv.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
