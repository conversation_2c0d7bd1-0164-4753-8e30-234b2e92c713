# AI-Examiner Platform

A comprehensive web application for generating and evaluating coding questions from uploaded documents using AI.

## Features

- **File Upload & Processing**: Support for .docx, .pdf, .txt, .xlsx, and image files
- **AI Question Generation**: Generate MCQs, coding problems, and aptitude questions using OpenAI
- **Multi-language Code Editor**: In-browser code editing and execution for Java, Python, C, C++, JavaScript, HTML
- **Code Analysis**: Complexity analysis, error correction, and improvement suggestions
- **Authentication**: Secure user registration and login with Supabase Auth
- **Real-time Execution**: Code compilation and execution using Judge0 API

## Tech Stack

### Backend
- **FastAPI**: Modern Python web framework
- **Supabase**: Authentication and PostgreSQL database
- **OpenAI**: AI-powered question generation and code analysis
- **Judge0**: Code execution engine
- **Python Libraries**: python-docx, pdfplumber, pytesseract, Pillow

### Frontend
- **HTML/CSS/JavaScript**: Modern vanilla JS with optional React
- **Supabase JS**: Client-side authentication
- **Monaco Editor**: Code editor component
- **Tailwind CSS**: Utility-first CSS framework

## Project Structure

```
ai-examiner/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── models/
│   │   ├── services/
│   │   ├── api/
│   │   └── utils/
│   ├── requirements.txt
│   └── .env.example
├── frontend/
│   ├── index.html
│   ├── login.html
│   ├── register.html
│   ├── dashboard.html
│   ├── editor.html
│   ├── css/
│   ├── js/
│   └── assets/
├── database/
│   └── migrations/
├── docker-compose.yml
└── README.md
```

## Setup Instructions

### Prerequisites
- Python 3.8+
- Node.js 16+
- Supabase account
- OpenAI API key
- Judge0 API access (optional: self-hosted)

### Backend Setup
1. Navigate to backend directory: `cd backend`
2. Create virtual environment: `python -m venv venv`
3. Activate virtual environment: `source venv/bin/activate` (Linux/Mac) or `venv\Scripts\activate` (Windows)
4. Install dependencies: `pip install -r requirements.txt`
5. Copy `.env.example` to `.env` and configure environment variables
6. Run the application: `uvicorn app.main:app --reload`

### Frontend Setup
1. Navigate to frontend directory: `cd frontend`
2. Install dependencies: `npm install`
3. Configure Supabase credentials in `js/config.js`
4. Serve the application: `python -m http.server 8000` or use a local server

### Database Setup
1. Create a new Supabase project
2. Run the SQL migrations in `database/migrations/`
3. Configure Row Level Security (RLS) policies

## Environment Variables

Create a `.env` file in the backend directory with:

```env
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key
OPENAI_API_KEY=your_openai_api_key
JUDGE0_API_KEY=your_judge0_api_key
JUDGE0_HOST=judge0-ce.p.rapidapi.com
SECRET_KEY=your_secret_key
```

## API Endpoints

- `POST /upload-file` - Upload and process files
- `GET /job/{job_id}` - Check processing status
- `POST /generate-questions` - Generate questions from text
- `GET /questions/{id}` - Get specific question
- `POST /submit-code` - Submit code for execution and analysis
- `GET /submissions/{user_id}` - Get user submissions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details
