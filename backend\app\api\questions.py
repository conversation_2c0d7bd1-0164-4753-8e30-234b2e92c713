"""
Question generation and management API endpoints for AI-Examiner.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from app.models.user import UserProfile
from app.models.question import QuestionGenerationRequest, Question, QuestionGenerationResponse
from app.services.question_generator import question_generator
from app.api.auth import get_current_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/questions", tags=["questions"])


@router.post("/generate", response_model=QuestionGenerationResponse)
async def generate_questions(
    request: QuestionGenerationRequest,
    current_user: UserProfile = Depends(get_current_user)
):
    """Generate questions from uploaded file."""
    try:
        job_id = await question_generator.generate_questions(request, current_user.id)
        
        return QuestionGenerationResponse(
            job_id=job_id,
            status="processing",
            message="Question generation started. Check job status for progress."
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Question generation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Question generation failed"
        )


@router.get("/job/{job_id}", response_model=QuestionGenerationResponse)
async def get_generation_job_status(
    job_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get question generation job status."""
    try:
        job_status = await question_generator.get_job_status(job_id)
        
        # Verify job ownership
        job_record = await question_generator.db.get_question_job(job_id)
        if not job_record or job_record['user_id'] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        response = QuestionGenerationResponse(
            job_id=job_id,
            status=job_status['status'],
            message=f"Job status: {job_status['status']}"
        )
        
        if job_status['status'] == 'completed':
            response.questions = [Question(**q) for q in job_status.get('questions', [])]
        elif job_status['status'] == 'failed':
            response.message = job_status.get('error_message', 'Job failed')
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )


@router.get("/", response_model=List[Question])
async def get_questions(
    question_type: Optional[str] = Query(None, description="Filter by question type"),
    difficulty: Optional[str] = Query(None, description="Filter by difficulty"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: UserProfile = Depends(get_current_user)
):
    """Get questions with optional filters."""
    try:
        filters = {}
        if question_type:
            filters['type'] = question_type
        if difficulty:
            filters['difficulty'] = difficulty
        
        questions = await question_generator.db.get_questions(filters, limit, offset)
        return [Question(**q) for q in questions]
        
    except Exception as e:
        logger.error(f"Error getting questions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get questions"
        )


@router.get("/{question_id}", response_model=Question)
async def get_question(
    question_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get a specific question by ID."""
    try:
        question = await question_generator.db.get_question(question_id)
        if not question:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Question not found"
            )
        
        return Question(**question)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting question: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get question"
        )


@router.get("/my/questions", response_model=List[Question])
async def get_my_questions(
    question_type: Optional[str] = Query(None, description="Filter by question type"),
    difficulty: Optional[str] = Query(None, description="Filter by difficulty"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: UserProfile = Depends(get_current_user)
):
    """Get questions created by current user."""
    try:
        filters = {"created_by": current_user.id}
        if question_type:
            filters['type'] = question_type
        if difficulty:
            filters['difficulty'] = difficulty
        
        questions = await question_generator.db.get_questions(filters, limit, offset)
        return [Question(**q) for q in questions]
        
    except Exception as e:
        logger.error(f"Error getting user questions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user questions"
        )


@router.delete("/{question_id}")
async def delete_question(
    question_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Delete a question (only by creator)."""
    try:
        question = await question_generator.db.get_question(question_id)
        if not question:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Question not found"
            )
        
        if question['created_by'] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Delete question (implement in database manager)
        await question_generator.db.delete_question(question_id)
        
        return {"message": "Question deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting question: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete question"
        )
