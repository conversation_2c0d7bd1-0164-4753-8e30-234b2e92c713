#!/usr/bin/env python3
"""
Setup script for AI-Examiner platform.
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, cwd=None):
    """Run a shell command and return the result."""
    try:
        result = subprocess.run(
            command, shell=True, cwd=cwd, 
            capture_output=True, text=True, check=True
        )
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running command '{command}': {e.stderr}")
        return None


def check_requirements():
    """Check if required tools are installed."""
    print("Checking requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required")
        return False
    
    # Check if pip is available
    if not shutil.which("pip"):
        print("Error: pip is not installed")
        return False
    
    # Check if node is available (optional)
    if not shutil.which("node"):
        print("Warning: Node.js is not installed (optional for frontend development)")
    
    print("✓ Requirements check passed")
    return True


def setup_backend():
    """Set up the backend environment."""
    print("\nSetting up backend...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("Error: Backend directory not found")
        return False
    
    # Create virtual environment
    venv_path = backend_dir / "venv"
    if not venv_path.exists():
        print("Creating virtual environment...")
        if not run_command("python -m venv venv", cwd=backend_dir):
            return False
    
    # Determine activation script path
    if os.name == 'nt':  # Windows
        activate_script = venv_path / "Scripts" / "activate"
        pip_path = venv_path / "Scripts" / "pip"
    else:  # Unix/Linux/macOS
        activate_script = venv_path / "bin" / "activate"
        pip_path = venv_path / "bin" / "pip"
    
    # Install requirements
    print("Installing Python dependencies...")
    requirements_file = backend_dir / "requirements.txt"
    if requirements_file.exists():
        if not run_command(f"{pip_path} install -r requirements.txt", cwd=backend_dir):
            return False
    else:
        print("Warning: requirements.txt not found")
    
    # Create .env file if it doesn't exist
    env_file = backend_dir / ".env"
    env_example = backend_dir / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("Creating .env file from template...")
        shutil.copy(env_example, env_file)
        print("⚠️  Please edit backend/.env with your actual configuration values")
    
    print("✓ Backend setup completed")
    return True


def setup_frontend():
    """Set up the frontend environment."""
    print("\nSetting up frontend...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("Error: Frontend directory not found")
        return False
    
    # Check if package.json exists
    package_json = frontend_dir / "package.json"
    if package_json.exists() and shutil.which("npm"):
        print("Installing Node.js dependencies...")
        if not run_command("npm install", cwd=frontend_dir):
            print("Warning: npm install failed, but frontend can still work with CDN dependencies")
    
    # Check configuration
    config_file = frontend_dir / "js" / "config.js"
    if config_file.exists():
        print("⚠️  Please edit frontend/js/config.js with your Supabase configuration")
    
    print("✓ Frontend setup completed")
    return True


def setup_database():
    """Set up database migrations."""
    print("\nDatabase setup...")
    
    migrations_dir = Path("database") / "migrations"
    if migrations_dir.exists():
        print("Database migration files found:")
        for migration in sorted(migrations_dir.glob("*.sql")):
            print(f"  - {migration.name}")
        print("⚠️  Please run these migrations in your Supabase dashboard or PostgreSQL client")
    
    print("✓ Database setup instructions provided")
    return True


def create_directories():
    """Create necessary directories."""
    print("\nCreating directories...")
    
    directories = [
        "backend/uploads",
        "backend/logs",
        "frontend/assets"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ Created {directory}")
    
    return True


def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "="*60)
    print("🎉 AI-Examiner setup completed!")
    print("="*60)
    print("\nNext steps:")
    print("\n1. Configure your environment:")
    print("   - Edit backend/.env with your API keys and database credentials")
    print("   - Edit frontend/js/config.js with your Supabase configuration")
    
    print("\n2. Set up your database:")
    print("   - Create a Supabase project at https://supabase.com")
    print("   - Run the SQL migrations in database/migrations/ in order")
    print("   - Configure Row Level Security (RLS) policies")
    
    print("\n3. Start the application:")
    print("   Backend:")
    print("   cd backend")
    if os.name == 'nt':
        print("   venv\\Scripts\\activate")
    else:
        print("   source venv/bin/activate")
    print("   uvicorn app.main:app --reload")
    
    print("\n   Frontend:")
    print("   cd frontend")
    print("   python -m http.server 8000")
    print("   # Or use any static file server")
    
    print("\n4. Access the application:")
    print("   - Frontend: http://localhost:8000")
    print("   - Backend API: http://localhost:8000/docs")
    
    print("\n5. Optional: Use Docker")
    print("   docker-compose up")
    
    print("\nFor more information, see README.md")
    print("="*60)


def main():
    """Main setup function."""
    print("AI-Examiner Platform Setup")
    print("="*30)
    
    if not check_requirements():
        sys.exit(1)
    
    if not create_directories():
        sys.exit(1)
    
    if not setup_backend():
        sys.exit(1)
    
    if not setup_frontend():
        sys.exit(1)
    
    if not setup_database():
        sys.exit(1)
    
    print_next_steps()


if __name__ == "__main__":
    main()
