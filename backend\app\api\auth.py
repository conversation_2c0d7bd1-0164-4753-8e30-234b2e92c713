"""
Authentication API endpoints for AI-Examiner.
"""
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from app.models.user import UserRegistration, UserLogin, UserProfile, UserProfileUpdate, AuthResponse
from app.services.auth import auth_service
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["authentication"])
security = HTTPBearer()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> UserProfile:
    """Get current authenticated user."""
    try:
        return await auth_service.get_current_user(credentials.credentials)
    except Exception as e:
        logger.error(f"Authentication error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


@router.post("/register", response_model=UserProfile, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserRegistration):
    """Register a new user."""
    try:
        user = await auth_service.register_user(user_data)
        return user
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=dict)
async def login(user_credentials: UserLogin):
    """Authenticate user and return access token."""
    try:
        user = await auth_service.authenticate_user(
            user_credentials.email, 
            user_credentials.password
        )
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # For Supabase Auth, we don't need to create our own JWT
        # The frontend will handle the Supabase session
        return {
            "message": "Login successful",
            "user": user,
            "token_type": "bearer"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.get("/me", response_model=UserProfile)
async def get_current_user_profile(current_user: UserProfile = Depends(get_current_user)):
    """Get current user profile."""
    return current_user


@router.put("/me", response_model=UserProfile)
async def update_profile(
    profile_update: UserProfileUpdate,
    current_user: UserProfile = Depends(get_current_user)
):
    """Update current user profile."""
    try:
        # Filter out None values
        update_data = {k: v for k, v in profile_update.dict().items() if v is not None}
        
        if not update_data:
            return current_user
        
        updated_user = await auth_service.update_user_profile(current_user.id, update_data)
        return updated_user
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Profile update failed"
        )


@router.post("/logout")
async def logout(current_user: UserProfile = Depends(get_current_user)):
    """Logout user (handled by frontend with Supabase)."""
    return {"message": "Logout successful"}


@router.post("/refresh")
async def refresh_token(current_user: UserProfile = Depends(get_current_user)):
    """Refresh access token (handled by Supabase)."""
    return {
        "message": "Token refresh handled by Supabase",
        "user": current_user
    }
