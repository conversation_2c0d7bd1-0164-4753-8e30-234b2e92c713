{"name": "ai-examiner-frontend", "version": "1.0.0", "description": "Frontend for AI-Examiner platform", "main": "index.html", "scripts": {"dev": "python -m http.server 8000", "build": "echo 'No build process needed for vanilla JS'", "serve": "python -m http.server 8000", "lint": "eslint js/**/*.js", "format": "prettier --write js/**/*.js css/**/*.css *.html"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "monaco-editor": "^0.44.0", "axios": "^1.6.2"}, "devDependencies": {"eslint": "^8.54.0", "prettier": "^3.1.0", "tailwindcss": "^3.3.6"}, "keywords": ["ai", "examiner", "code-editor", "education", "assessment"], "author": "AI-Examiner Team", "license": "MIT"}