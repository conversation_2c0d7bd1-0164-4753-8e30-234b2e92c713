"""
File upload and processing API endpoints for AI-Examiner.
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status
from app.models.user import UserProfile
from app.services.file_processor import file_processor
from app.api.auth import get_current_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/files", tags=["files"])


@router.post("/upload", status_code=status.HTTP_201_CREATED)
async def upload_file(
    file: UploadFile = File(...),
    current_user: UserProfile = Depends(get_current_user)
):
    """Upload a file for processing."""
    try:
        file_record = await file_processor.upload_file(file, current_user.id)
        return {
            "message": "File uploaded successfully",
            "file_id": file_record['id'],
            "file_name": file_record['file_name'],
            "file_type": file_record['file_type'],
            "file_size": file_record['file_size'],
            "status": file_record['processing_status']
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"File upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File upload failed"
        )


@router.post("/extract/{file_id}")
async def extract_text(
    file_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Extract text from uploaded file."""
    try:
        # Verify file ownership
        file_record = await file_processor.db.get_file_upload(file_id)
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        if file_record['user_id'] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        extracted_text = await file_processor.extract_text(file_id)
        
        return {
            "message": "Text extraction completed",
            "file_id": file_id,
            "extracted_text": extracted_text,
            "text_length": len(extracted_text)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Text extraction error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Text extraction failed"
        )


@router.get("/status/{file_id}")
async def get_file_status(
    file_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get file processing status."""
    try:
        file_record = await file_processor.db.get_file_upload(file_id)
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        if file_record['user_id'] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return {
            "file_id": file_id,
            "file_name": file_record['file_name'],
            "file_type": file_record['file_type'],
            "file_size": file_record['file_size'],
            "processing_status": file_record['processing_status'],
            "error_message": file_record.get('error_message'),
            "created_at": file_record['created_at'],
            "updated_at": file_record['updated_at']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting file status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get file status"
        )


@router.get("/my-files")
async def get_user_files(
    limit: int = 50,
    offset: int = 0,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get user's uploaded files."""
    try:
        files = await file_processor.db.get_user_files(current_user.id, limit, offset)
        return {
            "files": files,
            "total": len(files),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"Error getting user files: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user files"
        )


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Delete uploaded file."""
    try:
        # Verify file ownership
        file_record = await file_processor.db.get_file_upload(file_id)
        if not file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        if file_record['user_id'] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Delete file record (implement in database manager)
        await file_processor.db.delete_file_upload(file_id)
        
        return {"message": "File deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )
