"""
Code analysis and suggestion service using OpenAI for AI-Examiner.
"""
import json
import ast
import re
from typing import Dict, Any, List, Optional
from openai import OpenAI
from app.config import settings
from app.database import db_manager
from app.models.submission import CodeAnalysis, ExecutionResult
import logging

logger = logging.getLogger(__name__)

# Initialize OpenAI client
openai_client = OpenAI(api_key=settings.openai_api_key)


class CodeAnalyzer:
    """Code analysis service using OpenAI and static analysis."""
    
    def __init__(self):
        self.db = db_manager
        self.client = openai_client
    
    def _get_analysis_prompt(self, code: str, language: str, execution_result: Optional[ExecutionResult] = None) -> str:
        """Generate prompt for code analysis."""
        execution_info = ""
        if execution_result:
            execution_info = f"""
Execution Results:
- Status: {execution_result.status}
- Execution Time: {execution_result.time}s
- Memory Usage: {execution_result.memory} KB
- Output: {execution_result.stdout or 'None'}
- Errors: {execution_result.stderr or 'None'}
"""
        
        return f"""
You are an expert software engineer and code reviewer. Analyze the following {language} code and provide a comprehensive assessment.

Code:
```{language}
{code}
```

{execution_info}

Please provide your analysis in the following JSON format:
{{
  "time_complexity": "O(n) - Brief explanation of time complexity",
  "space_complexity": "O(1) - Brief explanation of space complexity",
  "code_quality": {{
    "readability": 8,
    "maintainability": 7,
    "efficiency": 6,
    "style_score": 9,
    "comments": "Assessment of code quality aspects"
  }},
  "suggestions": [
    "Specific improvement suggestion 1",
    "Specific improvement suggestion 2",
    "Specific improvement suggestion 3"
  ],
  "security_issues": [
    "Security concern 1 (if any)",
    "Security concern 2 (if any)"
  ],
  "performance_tips": [
    "Performance improvement tip 1",
    "Performance improvement tip 2"
  ],
  "corrected_code": "Improved version of the code (if simple fixes are possible, otherwise null)",
  "best_practices": [
    "Best practice recommendation 1",
    "Best practice recommendation 2"
  ],
  "edge_cases": [
    "Potential edge case issue 1",
    "Potential edge case issue 2"
  ]
}}

Focus on:
1. Algorithmic complexity analysis
2. Code quality and style
3. Potential bugs or issues
4. Performance optimizations
5. Security considerations
6. Best practices for the language
7. Edge cases that might not be handled

Be specific and actionable in your suggestions.
"""
    
    def _get_error_correction_prompt(self, code: str, language: str, error_message: str) -> str:
        """Generate prompt for error correction."""
        return f"""
You are a debugging expert. The following {language} code has an error. Please analyze the error and provide corrections.

Code:
```{language}
{code}
```

Error Message:
{error_message}

Please provide your response in the following JSON format:
{{
  "root_cause": "Explanation of what caused the error",
  "error_type": "Type of error (syntax, runtime, logic, etc.)",
  "fixes": [
    {{
      "description": "Description of fix 1",
      "corrected_code": "Fixed code snippet or full corrected code"
    }},
    {{
      "description": "Description of fix 2",
      "corrected_code": "Alternative fix if applicable"
    }}
  ],
  "explanation": "Detailed explanation of the error and how the fixes address it",
  "prevention_tips": [
    "Tip to prevent similar errors in the future",
    "Another prevention tip"
  ]
}}

Focus on:
1. Identifying the exact cause of the error
2. Providing working corrected code
3. Explaining why the error occurred
4. Suggesting ways to prevent similar errors
"""
    
    async def _call_openai(self, prompt: str) -> Dict[str, Any]:
        """Call OpenAI API for code analysis."""
        try:
            response = self.client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert code reviewer and software engineer. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,  # Lower temperature for more consistent analysis
                max_tokens=2000
            )
            
            content = response.choices[0].message.content.strip()
            
            # Try to parse JSON response
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # If direct parsing fails, try to extract JSON from the response
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1
                if start_idx != -1 and end_idx != 0:
                    json_content = content[start_idx:end_idx]
                    return json.loads(json_content)
                else:
                    raise ValueError("No valid JSON found in response")
            
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise Exception(f"Failed to analyze code: {e}")
    
    def _static_analysis_python(self, code: str) -> Dict[str, Any]:
        """Perform static analysis on Python code."""
        try:
            tree = ast.parse(code)
            
            analysis = {
                "lines_of_code": len(code.split('\n')),
                "functions_count": 0,
                "classes_count": 0,
                "loops_count": 0,
                "complexity_estimate": "Low"
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    analysis["functions_count"] += 1
                elif isinstance(node, ast.ClassDef):
                    analysis["classes_count"] += 1
                elif isinstance(node, (ast.For, ast.While)):
                    analysis["loops_count"] += 1
            
            # Simple complexity estimation
            if analysis["loops_count"] > 2:
                analysis["complexity_estimate"] = "High"
            elif analysis["loops_count"] > 0:
                analysis["complexity_estimate"] = "Medium"
            
            return analysis
            
        except SyntaxError as e:
            return {"error": f"Syntax error: {e}", "lines_of_code": len(code.split('\n'))}
        except Exception as e:
            return {"error": f"Analysis error: {e}", "lines_of_code": len(code.split('\n'))}
    
    def _static_analysis_generic(self, code: str, language: str) -> Dict[str, Any]:
        """Perform basic static analysis on any language."""
        lines = code.split('\n')
        
        analysis = {
            "lines_of_code": len(lines),
            "non_empty_lines": len([line for line in lines if line.strip()]),
            "comment_lines": 0,
            "complexity_estimate": "Unknown"
        }
        
        # Count comment lines based on language
        comment_patterns = {
            "python": r'^\s*#',
            "java": r'^\s*//',
            "cpp": r'^\s*//',
            "c": r'^\s*//',
            "javascript": r'^\s*//',
            "html": r'^\s*<!--',
            "css": r'^\s*/\*'
        }
        
        pattern = comment_patterns.get(language.lower(), r'^\s*//')
        for line in lines:
            if re.match(pattern, line):
                analysis["comment_lines"] += 1
        
        return analysis
    
    async def analyze_code(
        self, 
        code: str, 
        language: str, 
        execution_result: Optional[ExecutionResult] = None,
        user_id: Optional[str] = None
    ) -> CodeAnalysis:
        """Analyze code and provide comprehensive feedback."""
        try:
            # Perform static analysis
            if language.lower() == "python":
                static_analysis = self._static_analysis_python(code)
            else:
                static_analysis = self._static_analysis_generic(code, language)
            
            # Get AI analysis
            prompt = self._get_analysis_prompt(code, language, execution_result)
            ai_analysis = await self._call_openai(prompt)
            
            # Combine results
            code_quality = ai_analysis.get("code_quality", {})
            code_quality.update(static_analysis)
            
            analysis = CodeAnalysis(
                time_complexity=ai_analysis.get("time_complexity", "Unknown"),
                space_complexity=ai_analysis.get("space_complexity", "Unknown"),
                code_quality=code_quality,
                suggestions=ai_analysis.get("suggestions", []),
                corrected_code=ai_analysis.get("corrected_code"),
                security_issues=ai_analysis.get("security_issues", []),
                performance_tips=ai_analysis.get("performance_tips", [])
            )
            
            # Log analysis activity
            if user_id:
                await self.db.log_activity({
                    "user_id": user_id,
                    "event_type": "code_analysis",
                    "event_data": {
                        "language": language,
                        "code_length": len(code),
                        "time_complexity": analysis.time_complexity,
                        "suggestions_count": len(analysis.suggestions)
                    }
                })
            
            return analysis
            
        except Exception as e:
            logger.error(f"Code analysis error: {e}")
            # Return basic analysis on error
            return CodeAnalysis(
                time_complexity="Analysis failed",
                space_complexity="Analysis failed",
                code_quality={"error": str(e)},
                suggestions=[f"Analysis failed: {e}"]
            )
    
    async def correct_errors(
        self, 
        code: str, 
        language: str, 
        error_message: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Provide error correction suggestions."""
        try:
            prompt = self._get_error_correction_prompt(code, language, error_message)
            correction_analysis = await self._call_openai(prompt)
            
            # Log error correction activity
            if user_id:
                await self.db.log_activity({
                    "user_id": user_id,
                    "event_type": "error_correction",
                    "event_data": {
                        "language": language,
                        "error_type": correction_analysis.get("error_type", "Unknown"),
                        "fixes_provided": len(correction_analysis.get("fixes", []))
                    }
                })
            
            return correction_analysis
            
        except Exception as e:
            logger.error(f"Error correction failed: {e}")
            return {
                "root_cause": "Analysis failed",
                "error_type": "Unknown",
                "fixes": [],
                "explanation": f"Error correction analysis failed: {e}",
                "prevention_tips": []
            }
    
    async def get_code_suggestions(
        self, 
        code: str, 
        language: str, 
        focus_areas: List[str] = None
    ) -> List[str]:
        """Get specific code improvement suggestions."""
        try:
            focus_text = ""
            if focus_areas:
                focus_text = f"Focus specifically on: {', '.join(focus_areas)}"
            
            prompt = f"""
Analyze the following {language} code and provide specific, actionable improvement suggestions.

{focus_text}

Code:
```{language}
{code}
```

Provide suggestions as a JSON array of strings:
["Suggestion 1", "Suggestion 2", "Suggestion 3"]

Focus on practical improvements that would make the code better.
"""
            
            response = self.client.chat.completions.create(
                model=settings.openai_model,
                messages=[
                    {"role": "system", "content": "You are a code improvement expert. Provide practical, specific suggestions."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.4,
                max_tokens=1000
            )
            
            content = response.choices[0].message.content.strip()
            
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # Extract suggestions from text if JSON parsing fails
                suggestions = []
                lines = content.split('\n')
                for line in lines:
                    line = line.strip()
                    if line and (line.startswith('-') or line.startswith('*') or line.startswith('•')):
                        suggestions.append(line[1:].strip())
                return suggestions[:10]  # Limit to 10 suggestions
            
        except Exception as e:
            logger.error(f"Error getting code suggestions: {e}")
            return [f"Unable to generate suggestions: {e}"]


# Global code analyzer instance
code_analyzer = CodeAnalyzer()
