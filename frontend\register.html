<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - AI-Examiner</title>
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <header class="header">
        <div class="container clearfix">
            <h1><a href="index.html" style="color: white; text-decoration: none;">AI-Examiner</a></h1>
            <nav class="nav">
                <a href="index.html">Home</a>
                <a href="login.html">Login</a>
            </nav>
        </div>
    </header>

    <main class="main">
        <div class="container">
            <div class="row">
                <div class="col-2" style="margin: 0 auto;">
                    <div class="card">
                        <div class="card-header text-center">
                            <h2 class="card-title">Create Your Account</h2>
                            <p style="color: #666;">Join AI-Examiner and start creating smart assessments.</p>
                        </div>

                        <div id="alert-container"></div>

                        <form id="register-form">
                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="firstName" class="form-label">First Name</label>
                                        <input 
                                            type="text" 
                                            id="firstName" 
                                            name="firstName" 
                                            class="form-input" 
                                            required 
                                            placeholder="Enter your first name"
                                        >
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="lastName" class="form-label">Last Name</label>
                                        <input 
                                            type="text" 
                                            id="lastName" 
                                            name="lastName" 
                                            class="form-input" 
                                            required 
                                            placeholder="Enter your last name"
                                        >
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    class="form-input" 
                                    required 
                                    placeholder="Enter your email"
                                >
                            </div>

                            <div class="row">
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="dob" class="form-label">Date of Birth</label>
                                        <input 
                                            type="date" 
                                            id="dob" 
                                            name="dob" 
                                            class="form-input" 
                                            required
                                        >
                                    </div>
                                </div>
                                <div class="col-2">
                                    <div class="form-group">
                                        <label for="phone" class="form-label">Phone (Optional)</label>
                                        <input 
                                            type="tel" 
                                            id="phone" 
                                            name="phone" 
                                            class="form-input" 
                                            placeholder="Enter your phone number"
                                        >
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="password" class="form-label">Password</label>
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    class="form-input" 
                                    required 
                                    placeholder="Create a strong password"
                                >
                                <small style="color: #666; font-size: 0.9rem;">
                                    Password must be 8-15 characters with uppercase, lowercase, digit, and special character.
                                </small>
                            </div>

                            <div class="form-group">
                                <label for="confirmPassword" class="form-label">Confirm Password</label>
                                <input 
                                    type="password" 
                                    id="confirmPassword" 
                                    name="confirmPassword" 
                                    class="form-input" 
                                    required 
                                    placeholder="Confirm your password"
                                >
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary" style="width: 100%;">
                                    <span id="register-btn-text">Create Account</span>
                                    <div id="register-spinner" class="spinner" style="display: none; width: 20px; height: 20px; margin: 0 auto;"></div>
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-3">
                            <p>Already have an account? <a href="login.html" style="color: #667eea; text-decoration: none;">Sign in here</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 AI-Examiner. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="js/config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/api.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const registerForm = document.getElementById('register-form');
            const alertContainer = document.getElementById('alert-container');
            const registerBtnText = document.getElementById('register-btn-text');
            const registerSpinner = document.getElementById('register-spinner');

            function showAlert(message, type = 'error') {
                alertContainer.innerHTML = `
                    <div class="alert alert-${type}">
                        ${message}
                    </div>
                `;
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 8000);
            }

            function setLoading(loading) {
                if (loading) {
                    registerBtnText.style.display = 'none';
                    registerSpinner.style.display = 'block';
                    registerForm.querySelector('button').disabled = true;
                } else {
                    registerBtnText.style.display = 'inline';
                    registerSpinner.style.display = 'none';
                    registerForm.querySelector('button').disabled = false;
                }
            }

            registerForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(registerForm);
                const userData = {
                    firstName: formData.get('firstName'),
                    lastName: formData.get('lastName'),
                    email: formData.get('email'),
                    dob: formData.get('dob'),
                    phone: formData.get('phone'),
                    password: formData.get('password')
                };

                const confirmPassword = formData.get('confirmPassword');

                // Validation
                if (!userData.firstName || !userData.lastName || !userData.email || !userData.dob || !userData.password) {
                    showAlert('Please fill in all required fields.');
                    return;
                }

                if (userData.password !== confirmPassword) {
                    showAlert('Passwords do not match.');
                    return;
                }

                if (!CONFIG.PASSWORD_PATTERN.test(userData.password)) {
                    showAlert('Password must be 8-15 characters long and contain at least one lowercase letter, one uppercase letter, one digit, and one special character.');
                    return;
                }

                setLoading(true);

                try {
                    const result = await authService.register(userData);
                    
                    if (result.success) {
                        showAlert('Registration successful! Please check your email for verification.', 'success');
                        setTimeout(() => {
                            window.location.href = 'login.html';
                        }, 3000);
                    } else {
                        showAlert(result.error || 'Registration failed. Please try again.');
                    }
                } catch (error) {
                    showAlert('An error occurred. Please try again.');
                    console.error('Registration error:', error);
                } finally {
                    setLoading(false);
                }
            });

            // Auto-focus first name field
            document.getElementById('firstName').focus();
        });
    </script>
</body>
</html>
