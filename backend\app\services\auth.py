"""
Authentication service for AI-Examiner.
"""
import random
import string
from datetime import datetime, timedelta
from typing import Optional
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from app.config import settings
from app.database import db_manager, supabase
from app.models.user import UserRegistration, UserProfile
import logging

logger = logging.getLogger(__name__)

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def generate_unique_user_id(first_name: str, dob: datetime.date) -> str:
    """
    Generate unique user ID in format: CHAR1998AZ123
    - First 4 characters of name (padded with X if needed)
    - Year of birth
    - 2 random uppercase letters
    - 3 random digits
    """
    name_part = (first_name[:4].upper() + 'XXXX')[:4]
    year = dob.year
    letters = ''.join(random.choices(string.ascii_uppercase, k=2))
    digits = f"{random.randint(0, 999):03d}"
    return f"{name_part}{year}{letters}{digits}"


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(token: str) -> dict:
    """Verify JWT token and return payload."""
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


class AuthService:
    """Authentication service class."""
    
    def __init__(self):
        self.db = db_manager
    
    async def register_user(self, user_data: UserRegistration) -> UserProfile:
        """Register a new user."""
        try:
            # Generate unique user ID
            unique_id = generate_unique_user_id(user_data.first_name, user_data.dob)
            
            # Check if unique ID already exists (very unlikely but possible)
            existing_profile = None
            try:
                result = supabase.table('profiles').select('unique_user_id').eq('unique_user_id', unique_id).execute()
                if result.data:
                    # Generate a new one if collision occurs
                    unique_id = generate_unique_user_id(user_data.first_name, user_data.dob)
            except:
                pass  # Continue with the generated ID
            
            # Create user in Supabase Auth
            auth_response = supabase.auth.sign_up({
                "email": user_data.email,
                "password": user_data.password,
                "options": {
                    "data": {
                        "first_name": user_data.first_name,
                        "last_name": user_data.last_name,
                        "dob": user_data.dob.isoformat(),
                        "phone": user_data.phone,
                        "unique_user_id": unique_id
                    }
                }
            })
            
            if auth_response.user is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to create user account"
                )
            
            # Create profile in database
            profile_data = {
                "id": auth_response.user.id,
                "first_name": user_data.first_name,
                "last_name": user_data.last_name,
                "email": user_data.email,
                "dob": user_data.dob.isoformat(),
                "phone": user_data.phone,
                "unique_user_id": unique_id
            }
            
            profile = await self.db.create_profile(profile_data)
            
            # Log registration activity
            await self.db.log_activity({
                "user_id": auth_response.user.id,
                "event_type": "user_registration",
                "event_data": {"email": user_data.email, "unique_id": unique_id}
            })
            
            return UserProfile(**profile)
            
        except Exception as e:
            logger.error(f"Registration error: {e}")
            if "duplicate key value violates unique constraint" in str(e):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Registration failed"
            )
    
    async def authenticate_user(self, email: str, password: str) -> Optional[UserProfile]:
        """Authenticate user with email and password."""
        try:
            # Sign in with Supabase Auth
            auth_response = supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })
            
            if auth_response.user is None:
                return None
            
            # Get user profile
            profile = await self.db.get_profile(auth_response.user.id)
            if not profile:
                return None
            
            # Log login activity
            await self.db.log_activity({
                "user_id": auth_response.user.id,
                "event_type": "user_login",
                "event_data": {"email": email}
            })
            
            return UserProfile(**profile)
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    async def get_current_user(self, token: str) -> UserProfile:
        """Get current user from JWT token."""
        try:
            # Verify token with Supabase
            user_response = supabase.auth.get_user(token)
            
            if user_response.user is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            
            # Get user profile
            profile = await self.db.get_profile(user_response.user.id)
            if not profile:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User profile not found"
                )
            
            return UserProfile(**profile)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Get current user error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    
    async def update_user_profile(self, user_id: str, update_data: dict) -> UserProfile:
        """Update user profile."""
        try:
            profile = await self.db.update_profile(user_id, update_data)
            if not profile:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User profile not found"
                )
            
            # Log profile update activity
            await self.db.log_activity({
                "user_id": user_id,
                "event_type": "profile_update",
                "event_data": update_data
            })
            
            return UserProfile(**profile)
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Profile update error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update profile"
            )


# Global auth service instance
auth_service = AuthService()
