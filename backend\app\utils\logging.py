"""
Logging utilities for AI-Examiner.
"""
import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import Request
from app.database import db_manager


class ActivityLogger:
    """Enhanced activity logger for comprehensive audit trails."""
    
    def __init__(self):
        self.db = db_manager
        self.logger = logging.getLogger(__name__)
    
    async def log_user_activity(
        self,
        user_id: str,
        event_type: str,
        event_data: Dict[str, Any],
        request: Optional[Request] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """Log user activity with comprehensive details."""
        try:
            # Extract request information if available
            if request:
                ip_address = ip_address or self._get_client_ip(request)
                user_agent = user_agent or request.headers.get("user-agent", "")
            
            # Prepare log data
            log_data = {
                "user_id": user_id,
                "event_type": event_type,
                "event_data": event_data,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Log to database
            await self.db.log_activity(log_data)
            
            # Also log to application logger
            self.logger.info(f"User Activity: {event_type}", extra={
                "user_id": user_id,
                "event_data": event_data,
                "ip_address": ip_address
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to log user activity: {e}")
            return False
    
    async def log_system_event(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        severity: str = "info"
    ) -> bool:
        """Log system-level events."""
        try:
            log_data = {
                "user_id": None,  # System events don't have a user
                "event_type": f"system_{event_type}",
                "event_data": {
                    **event_data,
                    "severity": severity,
                    "timestamp": datetime.utcnow().isoformat()
                },
                "ip_address": None,
                "user_agent": "system"
            }
            
            await self.db.log_activity(log_data)
            
            # Log to application logger with appropriate level
            log_level = getattr(logging, severity.upper(), logging.INFO)
            self.logger.log(log_level, f"System Event: {event_type}", extra=event_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to log system event: {e}")
            return False
    
    async def log_security_event(
        self,
        event_type: str,
        event_data: Dict[str, Any],
        user_id: Optional[str] = None,
        request: Optional[Request] = None
    ) -> bool:
        """Log security-related events."""
        try:
            ip_address = None
            user_agent = None
            
            if request:
                ip_address = self._get_client_ip(request)
                user_agent = request.headers.get("user-agent", "")
            
            log_data = {
                "user_id": user_id,
                "event_type": f"security_{event_type}",
                "event_data": {
                    **event_data,
                    "severity": "warning",
                    "timestamp": datetime.utcnow().isoformat()
                },
                "ip_address": ip_address,
                "user_agent": user_agent
            }
            
            await self.db.log_activity(log_data)
            
            # Security events should be logged at WARNING level
            self.logger.warning(f"Security Event: {event_type}", extra={
                "user_id": user_id,
                "event_data": event_data,
                "ip_address": ip_address
            })
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to log security event: {e}")
            return False
    
    async def log_performance_metric(
        self,
        metric_name: str,
        metric_value: float,
        metric_unit: str,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Log performance metrics."""
        try:
            event_data = {
                "metric_name": metric_name,
                "metric_value": metric_value,
                "metric_unit": metric_unit,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            if additional_data:
                event_data.update(additional_data)
            
            return await self.log_system_event("performance_metric", event_data)
            
        except Exception as e:
            self.logger.error(f"Failed to log performance metric: {e}")
            return False
    
    async def log_api_request(
        self,
        request: Request,
        response_status: int,
        response_time: float,
        user_id: Optional[str] = None
    ) -> bool:
        """Log API request details."""
        try:
            event_data = {
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "response_status": response_status,
                "response_time_ms": round(response_time * 1000, 2),
                "timestamp": datetime.utcnow().isoformat()
            }
            
            if user_id:
                return await self.log_user_activity(
                    user_id, "api_request", event_data, request
                )
            else:
                return await self.log_system_event("api_request", event_data)
            
        except Exception as e:
            self.logger.error(f"Failed to log API request: {e}")
            return False
    
    async def get_user_activity_logs(
        self,
        user_id: str,
        event_types: Optional[list] = None,
        limit: int = 100,
        offset: int = 0
    ) -> list:
        """Get user activity logs with optional filtering."""
        try:
            # This would need to be implemented in the database manager
            # For now, return empty list
            return []
            
        except Exception as e:
            self.logger.error(f"Failed to get user activity logs: {e}")
            return []
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address from request."""
        # Check for forwarded headers first (for reverse proxies)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fallback to direct client IP
        return request.client.host if request.client else "unknown"


# Global activity logger instance
activity_logger = ActivityLogger()


# Decorator for automatic activity logging
def log_activity(event_type: str, include_args: bool = False):
    """Decorator to automatically log function calls as activities."""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                # Extract user_id if available
                user_id = kwargs.get('user_id') or getattr(kwargs.get('current_user'), 'id', None)
                
                event_data = {"function": func.__name__}
                if include_args:
                    event_data["args"] = str(args)
                    event_data["kwargs"] = {k: str(v) for k, v in kwargs.items()}
                
                # Execute function
                result = await func(*args, **kwargs)
                
                # Log successful execution
                if user_id:
                    await activity_logger.log_user_activity(
                        user_id, event_type, {**event_data, "status": "success"}
                    )
                
                return result
                
            except Exception as e:
                # Log failed execution
                if user_id:
                    await activity_logger.log_user_activity(
                        user_id, event_type, {**event_data, "status": "error", "error": str(e)}
                    )
                raise
        
        return wrapper
    return decorator
