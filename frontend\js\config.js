/**
 * Configuration file for AI-Examiner frontend
 */

// Supabase configuration
const SUPABASE_CONFIG = {
    url: 'https://your-project.supabase.co', // Replace with your Supabase URL
    anonKey: 'your-supabase-anon-key' // Replace with your Supabase anon key
};

// API configuration
const API_CONFIG = {
    baseUrl: 'http://localhost:8000/api/v1',
    timeout: 30000
};

// Application configuration
const APP_CONFIG = {
    name: 'AI-Examiner',
    version: '1.0.0',
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedFileTypes: ['.pdf', '.docx', '.doc', '.txt', '.xlsx', '.xls', '.png', '.jpg', '.jpeg', '.gif'],
    supportedLanguages: [
        { id: 'python', name: 'Python', extension: 'py' },
        { id: 'java', name: 'Java', extension: 'java' },
        { id: 'cpp', name: 'C++', extension: 'cpp' },
        { id: 'c', name: 'C', extension: 'c' },
        { id: 'javascript', name: 'JavaScript', extension: 'js' },
        { id: 'html', name: 'HTML', extension: 'html' },
        { id: 'css', name: 'CSS', extension: 'css' }
    ]
};

// Password validation pattern
const PASSWORD_PATTERN = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,15}$/;

// Export configurations
window.CONFIG = {
    SUPABASE: SUPABASE_CONFIG,
    API: API_CONFIG,
    APP: APP_CONFIG,
    PASSWORD_PATTERN
};
