"""
User models and schemas for AI-Examiner.
"""
from datetime import date, datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, validator
import re
from app.config import PASSWORD_PATTERN


class UserRegistration(BaseModel):
    """User registration schema."""
    first_name: str
    last_name: str
    email: EmailStr
    dob: date
    phone: Optional[str] = None
    password: str
    
    @validator('first_name', 'last_name')
    def validate_names(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Name must be at least 2 characters long')
        return v.strip().title()
    
    @validator('password')
    def validate_password(cls, v):
        if not re.match(PASSWORD_PATTERN, v):
            raise ValueError(
                'Password must be 8-15 characters long and contain at least one '
                'lowercase letter, one uppercase letter, one digit, and one special character'
            )
        return v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not re.match(r'^\+?[\d\s\-\(\)]{10,15}$', v):
            raise ValueError('Invalid phone number format')
        return v


class UserLogin(BaseModel):
    """User login schema."""
    email: EmailStr
    password: str


class UserProfile(BaseModel):
    """User profile schema."""
    id: str
    first_name: str
    last_name: str
    email: str
    dob: date
    phone: Optional[str] = None
    unique_user_id: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class UserProfileUpdate(BaseModel):
    """User profile update schema."""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone: Optional[str] = None
    
    @validator('first_name', 'last_name')
    def validate_names(cls, v):
        if v and len(v.strip()) < 2:
            raise ValueError('Name must be at least 2 characters long')
        return v.strip().title() if v else v
    
    @validator('phone')
    def validate_phone(cls, v):
        if v and not re.match(r'^\+?[\d\s\-\(\)]{10,15}$', v):
            raise ValueError('Invalid phone number format')
        return v


class AuthResponse(BaseModel):
    """Authentication response schema."""
    access_token: str
    token_type: str = "bearer"
    user: UserProfile


class TokenData(BaseModel):
    """Token data schema."""
    user_id: Optional[str] = None
    email: Optional[str] = None
