"""
Code submission and execution API endpoints for AI-Examiner.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from app.models.user import UserProfile
from app.models.submission import CodeSubmission, SubmissionResult, SubmissionResponse
from app.services.code_executor import code_executor
from app.services.code_analyzer import code_analyzer
from app.api.auth import get_current_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/submissions", tags=["submissions"])


@router.post("/submit", response_model=SubmissionResponse)
async def submit_code(
    submission: CodeSubmission,
    analyze: bool = Query(True, description="Whether to perform code analysis"),
    current_user: UserProfile = Depends(get_current_user)
):
    """Submit code for execution and analysis."""
    try:
        # Execute code
        result = await code_executor.execute_code(submission, current_user.id)
        
        # Prepare response
        response = SubmissionResponse(
            submission_id=result.id,
            execution_result=result.result,
            score=result.score,
            feedback=result.feedback or "Code executed successfully",
            status=result.status
        )
        
        # Perform code analysis if requested
        if analyze:
            try:
                analysis = await code_analyzer.analyze_code(
                    submission.code,
                    submission.language,
                    result.result,
                    current_user.id
                )
                response.analysis = analysis
                
                # Update feedback with analysis
                feedback_parts = [response.feedback]
                
                if analysis.suggestions:
                    feedback_parts.append("Suggestions:")
                    feedback_parts.extend([f"- {s}" for s in analysis.suggestions[:3]])
                
                if analysis.performance_tips:
                    feedback_parts.append("Performance Tips:")
                    feedback_parts.extend([f"- {t}" for t in analysis.performance_tips[:2]])
                
                response.feedback = "\n".join(feedback_parts)
                
            except Exception as e:
                logger.warning(f"Code analysis failed: {e}")
                # Continue without analysis
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Code submission error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Code submission failed"
        )


@router.get("/{submission_id}", response_model=SubmissionResult)
async def get_submission(
    submission_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get submission result by ID."""
    try:
        submission = await code_executor.get_submission_result(submission_id)
        
        # Verify ownership
        if submission.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return submission
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting submission: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get submission"
        )


@router.get("/", response_model=List[SubmissionResult])
async def get_user_submissions(
    question_id: Optional[str] = Query(None, description="Filter by question ID"),
    language: Optional[str] = Query(None, description="Filter by programming language"),
    status: Optional[str] = Query(None, description="Filter by execution status"),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: UserProfile = Depends(get_current_user)
):
    """Get user's submissions with optional filters."""
    try:
        submissions = await code_executor.get_user_submissions(current_user.id, limit, offset)
        
        # Apply filters
        if question_id:
            submissions = [s for s in submissions if s.question_id == question_id]
        if language:
            submissions = [s for s in submissions if s.language.lower() == language.lower()]
        if status:
            submissions = [s for s in submissions if s.status.lower() == status.lower()]
        
        return submissions
        
    except Exception as e:
        logger.error(f"Error getting user submissions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user submissions"
        )


@router.post("/analyze/{submission_id}")
async def analyze_submission(
    submission_id: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Analyze a specific submission."""
    try:
        submission = await code_executor.get_submission_result(submission_id)
        
        # Verify ownership
        if submission.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Perform analysis
        analysis = await code_analyzer.analyze_code(
            submission.code,
            submission.language,
            submission.result,
            current_user.id
        )
        
        return {
            "submission_id": submission_id,
            "analysis": analysis,
            "message": "Code analysis completed"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing submission: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze submission"
        )


@router.post("/correct-errors")
async def correct_code_errors(
    code: str,
    language: str,
    error_message: str,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get error correction suggestions for code."""
    try:
        corrections = await code_analyzer.correct_errors(
            code, language, error_message, current_user.id
        )
        
        return {
            "corrections": corrections,
            "message": "Error correction analysis completed"
        }
        
    except Exception as e:
        logger.error(f"Error correction failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error correction failed"
        )


@router.post("/suggestions")
async def get_code_suggestions(
    code: str,
    language: str,
    focus_areas: Optional[List[str]] = None,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get code improvement suggestions."""
    try:
        suggestions = await code_analyzer.get_code_suggestions(
            code, language, focus_areas
        )
        
        return {
            "suggestions": suggestions,
            "message": "Code suggestions generated"
        }
        
    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get code suggestions"
        )


@router.get("/stats/user")
async def get_user_stats(
    current_user: UserProfile = Depends(get_current_user)
):
    """Get user submission statistics."""
    try:
        submissions = await code_executor.get_user_submissions(current_user.id, 1000, 0)
        
        stats = {
            "total_submissions": len(submissions),
            "languages_used": list(set(s.language for s in submissions)),
            "average_score": sum(s.score for s in submissions) / len(submissions) if submissions else 0,
            "status_distribution": {},
            "recent_submissions": len([s for s in submissions[:10]])
        }
        
        # Calculate status distribution
        for submission in submissions:
            status = submission.status
            stats["status_distribution"][status] = stats["status_distribution"].get(status, 0) + 1
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting user stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user statistics"
        )
