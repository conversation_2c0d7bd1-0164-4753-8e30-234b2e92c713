/**
 * API service for AI-Examiner frontend
 */

class ApiService {
    constructor() {
        this.baseUrl = CONFIG.API.baseUrl;
        this.timeout = CONFIG.API.timeout;
    }

    async makeRequest(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        
        // Get auth token
        const token = await authService.getAuthToken();
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` })
            },
            timeout: this.timeout
        };

        const finalOptions = {
            ...defaultOptions,
            ...options,
            headers: {
                ...defaultOptions.headers,
                ...options.headers
            }
        };

        try {
            const response = await fetch(url, finalOptions);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));
                throw new Error(errorData.detail || `HTTP ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`API request failed: ${endpoint}`, error);
            throw error;
        }
    }

    // File operations
    async uploadFile(file) {
        const formData = new FormData();
        formData.append('file', file);

        return this.makeRequest('/files/upload', {
            method: 'POST',
            headers: {}, // Remove Content-Type to let browser set it for FormData
            body: formData
        });
    }

    async extractText(fileId) {
        return this.makeRequest(`/files/extract/${fileId}`, {
            method: 'POST'
        });
    }

    async getFileStatus(fileId) {
        return this.makeRequest(`/files/status/${fileId}`);
    }

    async getUserFiles(limit = 50, offset = 0) {
        return this.makeRequest(`/files/my-files?limit=${limit}&offset=${offset}`);
    }

    async deleteFile(fileId) {
        return this.makeRequest(`/files/${fileId}`, {
            method: 'DELETE'
        });
    }

    // Question operations
    async generateQuestions(requestData) {
        return this.makeRequest('/questions/generate', {
            method: 'POST',
            body: JSON.stringify(requestData)
        });
    }

    async getGenerationJobStatus(jobId) {
        return this.makeRequest(`/questions/job/${jobId}`);
    }

    async getQuestions(filters = {}) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value) params.append(key, value);
        });
        
        return this.makeRequest(`/questions/?${params.toString()}`);
    }

    async getQuestion(questionId) {
        return this.makeRequest(`/questions/${questionId}`);
    }

    async getMyQuestions(filters = {}) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value) params.append(key, value);
        });
        
        return this.makeRequest(`/questions/my/questions?${params.toString()}`);
    }

    async deleteQuestion(questionId) {
        return this.makeRequest(`/questions/${questionId}`, {
            method: 'DELETE'
        });
    }

    // Submission operations
    async submitCode(submissionData, analyze = true) {
        return this.makeRequest(`/submissions/submit?analyze=${analyze}`, {
            method: 'POST',
            body: JSON.stringify(submissionData)
        });
    }

    async getSubmission(submissionId) {
        return this.makeRequest(`/submissions/${submissionId}`);
    }

    async getUserSubmissions(filters = {}) {
        const params = new URLSearchParams();
        Object.entries(filters).forEach(([key, value]) => {
            if (value) params.append(key, value);
        });
        
        return this.makeRequest(`/submissions/?${params.toString()}`);
    }

    async analyzeSubmission(submissionId) {
        return this.makeRequest(`/submissions/analyze/${submissionId}`, {
            method: 'POST'
        });
    }

    async correctCodeErrors(code, language, errorMessage) {
        return this.makeRequest('/submissions/correct-errors', {
            method: 'POST',
            body: JSON.stringify({
                code,
                language,
                error_message: errorMessage
            })
        });
    }

    async getCodeSuggestions(code, language, focusAreas = null) {
        return this.makeRequest('/submissions/suggestions', {
            method: 'POST',
            body: JSON.stringify({
                code,
                language,
                focus_areas: focusAreas
            })
        });
    }

    async getUserStats() {
        return this.makeRequest('/submissions/stats/user');
    }

    // Utility methods
    async healthCheck() {
        return this.makeRequest('/health', { method: 'GET' });
    }
}

// Create global API service instance
window.apiService = new ApiService();
