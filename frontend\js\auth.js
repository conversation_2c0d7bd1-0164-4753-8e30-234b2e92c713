/**
 * Authentication service for AI-Examiner frontend
 */

class AuthService {
    constructor() {
        this.supabase = null;
        this.currentUser = null;
        this.init();
    }

    async init() {
        // Initialize Supabase client
        const { createClient } = supabase;
        this.supabase = createClient(CONFIG.SUPABASE.url, CONFIG.SUPABASE.anonKey);
        
        // Check for existing session
        const { data: { session } } = await this.supabase.auth.getSession();
        if (session) {
            this.currentUser = session.user;
            this.onAuthStateChange(session.user);
        }

        // Listen for auth state changes
        this.supabase.auth.onAuthStateChange((event, session) => {
            if (event === 'SIGNED_IN') {
                this.currentUser = session.user;
                this.onAuthStateChange(session.user);
            } else if (event === 'SIGNED_OUT') {
                this.currentUser = null;
                this.onAuthStateChange(null);
            }
        });
    }

    onAuthStateChange(user) {
        // Update UI based on auth state
        const authElements = document.querySelectorAll('[data-auth-required]');
        const noAuthElements = document.querySelectorAll('[data-no-auth-required]');

        if (user) {
            authElements.forEach(el => el.style.display = 'block');
            noAuthElements.forEach(el => el.style.display = 'none');
            
            // Update user info in UI
            const userNameElements = document.querySelectorAll('[data-user-name]');
            userNameElements.forEach(el => {
                el.textContent = user.user_metadata?.first_name || user.email;
            });
        } else {
            authElements.forEach(el => el.style.display = 'none');
            noAuthElements.forEach(el => el.style.display = 'block');
        }

        // Redirect based on auth state
        this.handleRedirect(user);
    }

    handleRedirect(user) {
        const currentPath = window.location.pathname;
        
        if (user) {
            // User is logged in
            if (currentPath === '/login.html' || currentPath === '/register.html' || currentPath === '/') {
                window.location.href = '/dashboard.html';
            }
        } else {
            // User is not logged in
            if (currentPath !== '/login.html' && currentPath !== '/register.html' && currentPath !== '/') {
                window.location.href = '/login.html';
            }
        }
    }

    async register(userData) {
        try {
            // Validate password
            if (!CONFIG.PASSWORD_PATTERN.test(userData.password)) {
                throw new Error('Password must be 8-15 characters long and contain at least one lowercase letter, one uppercase letter, one digit, and one special character');
            }

            // Generate unique user ID
            const uniqueId = this.generateUniqueId(userData.firstName, userData.dob);

            const { data, error } = await this.supabase.auth.signUp({
                email: userData.email,
                password: userData.password,
                options: {
                    data: {
                        first_name: userData.firstName,
                        last_name: userData.lastName,
                        dob: userData.dob,
                        phone: userData.phone,
                        unique_user_id: uniqueId
                    }
                }
            });

            if (error) throw error;

            return { success: true, data, message: 'Registration successful! Please check your email for verification.' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async login(email, password) {
        try {
            const { data, error } = await this.supabase.auth.signInWithPassword({
                email,
                password
            });

            if (error) throw error;

            return { success: true, data, message: 'Login successful!' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async logout() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;
            
            return { success: true, message: 'Logout successful!' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async getCurrentUser() {
        try {
            const { data: { user } } = await this.supabase.auth.getUser();
            return user;
        } catch (error) {
            console.error('Error getting current user:', error);
            return null;
        }
    }

    async getSession() {
        try {
            const { data: { session } } = await this.supabase.auth.getSession();
            return session;
        } catch (error) {
            console.error('Error getting session:', error);
            return null;
        }
    }

    generateUniqueId(firstName, dob) {
        // Format: first 4 characters of name + year + 2 random letters + 3 random digits
        const namePart = (firstName.substring(0, 4).toUpperCase() + 'XXXX').substring(0, 4);
        const year = new Date(dob).getFullYear();
        const letters = this.getRandomString(2, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ');
        const digits = this.getRandomString(3, '0123456789');
        
        return `${namePart}${year}${letters}${digits}`;
    }

    getRandomString(length, chars) {
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    async getAuthToken() {
        try {
            const session = await this.getSession();
            return session?.access_token || null;
        } catch (error) {
            console.error('Error getting auth token:', error);
            return null;
        }
    }
}

// Create global auth service instance
window.authService = new AuthService();
