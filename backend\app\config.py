"""
Configuration settings for the AI-Examiner application.
"""
import os
from typing import List
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    app_name: str = "AI-Examiner"
    debug: bool = os.getenv("DEBUG", "False").lower() == "true"
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here")
    algorithm: str = os.getenv("ALGORITHM", "HS256")
    access_token_expire_minutes: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # CORS
    cors_origins: List[str] = [
        "http://localhost:3000",
        "http://localhost:8000", 
        "http://127.0.0.1:8000"
    ]
    
    # Supabase
    supabase_url: str = os.getenv("SUPABASE_URL", "")
    supabase_anon_key: str = os.getenv("SUPABASE_ANON_KEY", "")
    supabase_service_key: str = os.getenv("SUPABASE_SERVICE_KEY", "")
    
    # OpenAI
    openai_api_key: str = os.getenv("OPENAI_API_KEY", "")
    openai_model: str = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
    
    # Judge0
    judge0_api_key: str = os.getenv("JUDGE0_API_KEY", "")
    judge0_host: str = os.getenv("JUDGE0_HOST", "judge0-ce.p.rapidapi.com")
    judge0_url: str = os.getenv("JUDGE0_URL", "https://judge0-ce.p.rapidapi.com")
    
    # File Upload
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "10485760"))  # 10MB
    allowed_extensions: List[str] = [
        ".pdf", ".docx", ".doc", ".txt", ".xlsx", ".xls",
        ".png", ".jpg", ".jpeg", ".gif"
    ]
    
    # Redis
    redis_url: str = os.getenv("REDIS_URL", "redis://localhost:6379/0")
    
    # Logging
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    log_file: str = os.getenv("LOG_FILE", "app.log")
    
    # Language IDs for Judge0
    language_ids: dict = {
        "python": 71,
        "java": 62,
        "cpp": 54,
        "c": 50,
        "javascript": 63,
        "html": 67,
        "css": 68,
        "sql": 82,
        "bash": 46,
        "go": 60,
        "rust": 73,
        "php": 68,
        "ruby": 72,
        "swift": 83,
        "kotlin": 78,
        "scala": 81,
        "r": 80,
        "perl": 85,
        "lua": 64
    }
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Password validation pattern
PASSWORD_PATTERN = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\w\s]).{8,15}$'

# Question types
QUESTION_TYPES = ["mcq", "code", "aptitude"]

# Difficulty levels
DIFFICULTY_LEVELS = ["easy", "medium", "hard"]

# File type mappings
FILE_TYPE_MAPPINGS = {
    "application/pdf": "pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    "application/msword": "doc",
    "text/plain": "txt",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
    "application/vnd.ms-excel": "xls",
    "image/png": "png",
    "image/jpeg": "jpg",
    "image/gif": "gif"
}
