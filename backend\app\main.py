"""
Main FastAPI application for AI-Examiner.
"""
import logging
from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from app.config import settings
from app.api import auth, files, questions, submissions
from app.database import db_manager

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.log_file),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting AI-Examiner application")
    
    # Test database connection
    try:
        # Test Supabase connection
        result = db_manager.client.table('profiles').select('id').limit(1).execute()
        logger.info("Database connection successful")
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down AI-Examiner application")


# Create FastAPI application
app = FastAPI(
    title="AI-Examiner API",
    description="AI-powered examination and code evaluation platform",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Add trusted host middleware for production
if not settings.debug:
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["localhost", "127.0.0.1", "*.yourdomain.com"]
    )


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Global exception: {exc}", exc_info=True)
    
    if isinstance(exc, HTTPException):
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail}
        )
    
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        # Test database connection
        result = db_manager.client.table('profiles').select('id').limit(1).execute()
        
        return {
            "status": "healthy",
            "version": "1.0.0",
            "database": "connected",
            "timestamp": "2024-01-01T00:00:00Z"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "version": "1.0.0",
                "database": "disconnected",
                "error": str(e)
            }
        )


# Root endpoint
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to AI-Examiner API",
        "version": "1.0.0",
        "docs": "/docs",
        "health": "/health"
    }


# Include API routers
app.include_router(auth.router, prefix="/api/v1")
app.include_router(files.router, prefix="/api/v1")
app.include_router(questions.router, prefix="/api/v1")
app.include_router(submissions.router, prefix="/api/v1")


# Middleware for request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all requests."""
    start_time = time.time()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(f"Response: {response.status_code} - {process_time:.4f}s")
    
    return response


# Additional endpoints for development
if settings.debug:
    @app.get("/debug/config")
    async def debug_config():
        """Debug configuration endpoint (development only)."""
        return {
            "debug": settings.debug,
            "cors_origins": settings.cors_origins,
            "supabase_url": settings.supabase_url[:20] + "..." if settings.supabase_url else None,
            "openai_model": settings.openai_model,
            "judge0_host": settings.judge0_host,
            "max_file_size": settings.max_file_size,
            "allowed_extensions": settings.allowed_extensions
        }
    
    @app.get("/debug/database")
    async def debug_database():
        """Debug database endpoint (development only)."""
        try:
            # Test various database operations
            profiles_count = db_manager.client.table('profiles').select('id', count='exact').execute()
            questions_count = db_manager.client.table('questions').select('id', count='exact').execute()
            submissions_count = db_manager.client.table('submissions').select('id', count='exact').execute()
            
            return {
                "database_status": "connected",
                "tables": {
                    "profiles": profiles_count.count,
                    "questions": questions_count.count,
                    "submissions": submissions_count.count
                }
            }
        except Exception as e:
            return {
                "database_status": "error",
                "error": str(e)
            }


# Import time for request logging
import time

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
