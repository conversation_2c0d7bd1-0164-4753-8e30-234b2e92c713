/* AI-Examiner Frontend Styles */

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header h1 {
    display: inline-block;
    font-size: 1.8rem;
    font-weight: 600;
}

.nav {
    float: right;
    margin-top: 0.5rem;
}

.nav a {
    color: white;
    text-decoration: none;
    margin-left: 2rem;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.nav a:hover {
    background-color: rgba(255,255,255,0.2);
}

.main {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

.footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}

/* Cards */
.card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.card-header {
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

/* Forms */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e1e1;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e1e1;
    border-radius: 5px;
    font-size: 1rem;
    background-color: white;
    cursor: pointer;
}

.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e1e1e1;
    border-radius: 5px;
    font-size: 1rem;
    resize: vertical;
    min-height: 120px;
    font-family: 'Courier New', monospace;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn-outline:hover {
    background-color: #667eea;
    color: white;
}

/* Alerts */
.alert {
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Loading spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Grid system */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: -10px;
}

.col {
    flex: 1;
    padding: 10px;
}

.col-2 {
    flex: 0 0 50%;
    padding: 10px;
}

.col-3 {
    flex: 0 0 33.333%;
    padding: 10px;
}

.col-4 {
    flex: 0 0 25%;
    padding: 10px;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Code editor styles */
.code-editor {
    border: 2px solid #e1e1e1;
    border-radius: 5px;
    overflow: hidden;
}

.code-editor-header {
    background-color: #f8f9fa;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-editor-content {
    height: 400px;
}

/* File upload area */
.file-upload {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.3s;
    cursor: pointer;
}

.file-upload:hover {
    border-color: #667eea;
}

.file-upload.dragover {
    border-color: #667eea;
    background-color: rgba(102, 126, 234, 0.1);
}

/* Question cards */
.question-card {
    border-left: 4px solid #667eea;
}

.question-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.question-type.mcq {
    background-color: #e3f2fd;
    color: #1976d2;
}

.question-type.code {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.question-type.aptitude {
    background-color: #e8f5e8;
    color: #388e3c;
}

.difficulty {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.difficulty.easy {
    background-color: #c8e6c9;
    color: #2e7d32;
}

.difficulty.medium {
    background-color: #fff3e0;
    color: #f57c00;
}

.difficulty.hard {
    background-color: #ffcdd2;
    color: #d32f2f;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .header h1 {
        font-size: 1.5rem;
    }
    
    .nav {
        float: none;
        margin-top: 1rem;
    }
    
    .nav a {
        display: block;
        margin: 0.5rem 0;
    }
    
    .row {
        flex-direction: column;
    }
    
    .col, .col-2, .col-3, .col-4 {
        flex: 1;
    }
    
    .card {
        padding: 1rem;
    }
}
