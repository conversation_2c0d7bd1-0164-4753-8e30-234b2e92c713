"""
Submission models and schemas for AI-Examiner.
"""
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, validator
from app.config import settings


class CodeSubmission(BaseModel):
    """Code submission schema."""
    question_id: str
    language: str
    code: str
    input_data: Optional[str] = ""
    
    @validator('language')
    def validate_language(cls, v):
        if v.lower() not in settings.language_ids:
            raise ValueError(f'Unsupported language: {v}. Supported: {", ".join(settings.language_ids.keys())}')
        return v.lower()
    
    @validator('code')
    def validate_code(cls, v):
        if not v or len(v.strip()) < 10:
            raise ValueError('Code must be at least 10 characters long')
        return v


class ExecutionResult(BaseModel):
    """Code execution result schema."""
    status: str
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    compile_output: Optional[str] = None
    time: Optional[float] = None
    memory: Optional[int] = None
    exit_code: Optional[int] = None
    token: Optional[str] = None


class SubmissionResult(BaseModel):
    """Complete submission result schema."""
    id: str
    user_id: str
    question_id: str
    language: str
    code: str
    input_data: Optional[str] = None
    expected_output: Optional[str] = None
    actual_output: Optional[str] = None
    execution_time: Optional[float] = None
    memory_usage: Optional[int] = None
    status: str
    result: Dict[str, Any]
    score: int = 0
    feedback: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CodeAnalysis(BaseModel):
    """Code analysis result schema."""
    time_complexity: str
    space_complexity: str
    code_quality: Dict[str, Any]
    suggestions: list
    corrected_code: Optional[str] = None
    security_issues: list = []
    performance_tips: list = []


class SubmissionResponse(BaseModel):
    """Submission response schema."""
    submission_id: str
    execution_result: ExecutionResult
    analysis: Optional[CodeAnalysis] = None
    score: int
    feedback: str
    status: str
