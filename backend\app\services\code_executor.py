"""
Code execution service using Judge0 API for AI-Examiner.
"""
import base64
import time
from typing import Dict, Any, Optional
import httpx
from app.config import settings
from app.database import db_manager
from app.models.submission import CodeSubmission, ExecutionResult, SubmissionResult
import logging

logger = logging.getLogger(__name__)


class CodeExecutor:
    """Code execution service using Judge0 API."""
    
    def __init__(self):
        self.db = db_manager
        self.judge0_url = settings.judge0_url
        self.api_key = settings.judge0_api_key
        self.api_host = settings.judge0_host
        self.language_ids = settings.language_ids
        
        # HTTP client with timeout
        self.client = httpx.AsyncClient(timeout=30.0)
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers for Judge0 API requests."""
        return {
            'X-RapidAPI-Host': self.api_host,
            'X-RapidAPI-Key': self.api_key,
            'Content-Type': 'application/json'
        }
    
    def _encode_base64(self, text: str) -> str:
        """Encode text to base64."""
        return base64.b64encode(text.encode('utf-8')).decode('utf-8')
    
    def _decode_base64(self, encoded_text: Optional[str]) -> Optional[str]:
        """Decode base64 text."""
        if not encoded_text:
            return None
        try:
            return base64.b64decode(encoded_text).decode('utf-8')
        except Exception:
            return encoded_text  # Return as-is if decoding fails
    
    async def submit_code(self, submission: CodeSubmission, user_id: str) -> str:
        """Submit code for execution and return submission ID."""
        try:
            # Get language ID
            language_id = self.language_ids.get(submission.language)
            if not language_id:
                raise ValueError(f"Unsupported language: {submission.language}")
            
            # Prepare submission data
            submission_data = {
                "source_code": self._encode_base64(submission.code),
                "language_id": language_id,
                "stdin": self._encode_base64(submission.input_data) if submission.input_data else "",
                "base64_encoded": True,
                "wait": False  # Async submission
            }
            
            # Submit to Judge0
            response = await self.client.post(
                f"{self.judge0_url}/submissions",
                headers=self._get_headers(),
                json=submission_data
            )
            
            if response.status_code != 201:
                raise Exception(f"Judge0 submission failed: {response.text}")
            
            result = response.json()
            token = result.get('token')
            
            if not token:
                raise Exception("No token received from Judge0")
            
            # Create submission record in database
            db_submission_data = {
                "user_id": user_id,
                "question_id": submission.question_id,
                "language": submission.language,
                "code": submission.code,
                "input_data": submission.input_data,
                "status": "pending",
                "result": {"token": token, "judge0_status": "submitted"}
            }
            
            db_submission = await self.db.create_submission(db_submission_data)
            submission_id = db_submission['id']
            
            # Log submission activity
            await self.db.log_activity({
                "user_id": user_id,
                "event_type": "code_submission",
                "event_data": {
                    "submission_id": submission_id,
                    "question_id": submission.question_id,
                    "language": submission.language,
                    "code_length": len(submission.code)
                }
            })
            
            return submission_id
            
        except Exception as e:
            logger.error(f"Code submission error: {e}")
            raise Exception(f"Code submission failed: {e}")
    
    async def get_execution_result(self, token: str) -> ExecutionResult:
        """Get execution result from Judge0."""
        try:
            response = await self.client.get(
                f"{self.judge0_url}/submissions/{token}",
                headers=self._get_headers(),
                params={"base64_encoded": "true"}
            )
            
            if response.status_code != 200:
                raise Exception(f"Failed to get result: {response.text}")
            
            result = response.json()
            
            # Decode base64 encoded fields
            stdout = self._decode_base64(result.get('stdout'))
            stderr = self._decode_base64(result.get('stderr'))
            compile_output = self._decode_base64(result.get('compile_output'))
            
            return ExecutionResult(
                status=result.get('status', {}).get('description', 'Unknown'),
                stdout=stdout,
                stderr=stderr,
                compile_output=compile_output,
                time=result.get('time'),
                memory=result.get('memory'),
                exit_code=result.get('exit_code'),
                token=token
            )
            
        except Exception as e:
            logger.error(f"Error getting execution result: {e}")
            raise Exception(f"Failed to get execution result: {e}")
    
    async def wait_for_result(self, token: str, max_wait: int = 30) -> ExecutionResult:
        """Wait for execution result with polling."""
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            try:
                result = await self.get_execution_result(token)
                
                # Check if execution is complete
                if result.status not in ['In Queue', 'Processing']:
                    return result
                
                # Wait before next poll
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error polling result: {e}")
                await asyncio.sleep(2)
        
        # Timeout reached
        return ExecutionResult(
            status="Time Limit Exceeded",
            stderr="Execution timeout reached"
        )
    
    async def execute_code(self, submission: CodeSubmission, user_id: str) -> SubmissionResult:
        """Execute code and return complete result."""
        try:
            # Submit code
            submission_id = await self.submit_code(submission, user_id)
            
            # Get submission record to get token
            db_submission = await self.db.get_submission(submission_id)
            if not db_submission:
                raise Exception("Submission record not found")
            
            token = db_submission['result'].get('token')
            if not token:
                raise Exception("No execution token found")
            
            # Wait for execution result
            execution_result = await self.wait_for_result(token)
            
            # Update submission with results
            update_data = {
                "actual_output": execution_result.stdout,
                "execution_time": execution_result.time,
                "memory_usage": execution_result.memory,
                "status": execution_result.status,
                "result": {
                    "token": token,
                    "status": execution_result.status,
                    "stdout": execution_result.stdout,
                    "stderr": execution_result.stderr,
                    "compile_output": execution_result.compile_output,
                    "time": execution_result.time,
                    "memory": execution_result.memory,
                    "exit_code": execution_result.exit_code
                }
            }
            
            # Calculate basic score
            score = self._calculate_score(execution_result)
            update_data["score"] = score
            
            # Update database
            updated_submission = await self.db.update_submission(submission_id, update_data)
            
            # Log execution completion
            await self.db.log_activity({
                "user_id": user_id,
                "event_type": "code_execution_completed",
                "event_data": {
                    "submission_id": submission_id,
                    "status": execution_result.status,
                    "execution_time": execution_result.time,
                    "memory_usage": execution_result.memory,
                    "score": score
                }
            })
            
            return SubmissionResult(**updated_submission)
            
        except Exception as e:
            logger.error(f"Code execution error: {e}")
            
            # Update submission with error
            if 'submission_id' in locals():
                await self.db.update_submission(submission_id, {
                    "status": "error",
                    "result": {"error": str(e)}
                })
            
            raise Exception(f"Code execution failed: {e}")
    
    def _calculate_score(self, result: ExecutionResult) -> int:
        """Calculate basic score based on execution result."""
        if result.status == "Accepted":
            return 100
        elif result.status in ["Wrong Answer", "Presentation Error"]:
            return 50
        elif result.status in ["Compilation Error", "Runtime Error"]:
            return 25
        elif result.status in ["Time Limit Exceeded", "Memory Limit Exceeded"]:
            return 10
        else:
            return 0
    
    async def get_submission_result(self, submission_id: str) -> SubmissionResult:
        """Get submission result by ID."""
        try:
            submission = await self.db.get_submission(submission_id)
            if not submission:
                raise Exception("Submission not found")
            
            return SubmissionResult(**submission)
            
        except Exception as e:
            logger.error(f"Error getting submission result: {e}")
            raise Exception(f"Failed to get submission result: {e}")
    
    async def get_user_submissions(self, user_id: str, limit: int = 50, offset: int = 0) -> list:
        """Get user's submissions."""
        try:
            submissions = await self.db.get_submissions(user_id, limit, offset)
            return [SubmissionResult(**sub) for sub in submissions]
            
        except Exception as e:
            logger.error(f"Error getting user submissions: {e}")
            raise Exception(f"Failed to get user submissions: {e}")


# Import asyncio for sleep function
import asyncio

# Global code executor instance
code_executor = CodeExecutor()
